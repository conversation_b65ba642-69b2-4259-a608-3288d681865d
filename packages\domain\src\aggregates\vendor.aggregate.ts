import type { <PERSON>b<PERSON><PERSON><PERSON> } from '@rie/db-schema/entity-types';
import { DbUtils } from '@rie/utils';
import * as Data from 'effect/Data';
import * as Effect from 'effect/Effect';
import { pipe } from 'effect/Function';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';
import * as R from 'remeda';
import {
  type DuplicateLocaleError,
  TranslationValidationError,
  VendorInvariantViolationError,
} from '../errors';
import { DbVendorDataSchema } from '../schemas';
import type { VendorData } from '../types';
import {
  VendorTranslation,
  VendorTranslationCollection,
} from '../value-objects';

// Internal aggregate state. `createdAt` and `updatedAt` are optional
// because they do not exist on a new, in-memory aggregate.
interface VendorAggregateState
  extends Omit<DbVendor, 'createdAt' | 'updatedAt'> {
  createdAt?: string;
  updatedAt?: string;
  translations: VendorTranslationCollection;
}

const VendorAggregateState = Data.case<VendorAggregateState>();

// Vendor Aggregate Root
export class Vendor {
  private constructor(private readonly data: VendorAggregateState) { }

  /**
   * Factory for creating a NEW Vendor aggregate.
   * Use this when creating a vendor from user input for the first time.
   */
  static create(props: {
    isActive?: boolean;
    startDate?: string | null;
    endDate?: string | null;
    modifiedBy: string;
    translations: VendorTranslationCollection;
  }): Effect.Effect<Vendor, VendorInvariantViolationError> {
    const now = new Date().toISOString();
    const newVendor = new Vendor(
      VendorAggregateState({
        id: DbUtils.cuid2(),
        isActive: props.isActive ?? true,
        // Set temporary timestamps to satisfy toRaw() validation
        createdAt: now as string,
        updatedAt: now as string,
        startDate: props.startDate ?? null,
        endDate: props.endDate ?? null,
        modifiedBy: props.modifiedBy,
        translations: props.translations,
      }),
    );

    // Ensure the new vendor is valid before returning it
    return pipe(
      newVendor.validateInvariants(),
      Effect.map(() => newVendor),
    );
  }

  /**
   * Factory for rehydrating a Vendor aggregate from raw database data.
   */
  static fromDatabaseData(
    rawData: VendorData,
  ): Effect.Effect<
    Vendor,
    | TranslationValidationError
    | ParseResult.ParseError
    | VendorInvariantViolationError
    | DuplicateLocaleError
  > {
    return pipe(
      Schema.decodeUnknown(DbVendorDataSchema)(rawData),
      Effect.mapError((parseError) =>
        TranslationValidationError.forField(
          'Vendor',
          'database_validation',
          `Vendor database data validation failed: ${parseError.message}`,
        ),
      ),
      Effect.flatMap((validated) =>
        pipe(
          validated.translations.map((t) => VendorTranslation.create(t)),
          Effect.all,
          Effect.flatMap(VendorTranslationCollection.create),
          Effect.map((translations) => ({ ...validated, translations })),
        ),
      ),
      Effect.map(
        (hydratedData) =>
          new Vendor(
            VendorAggregateState({
              ...hydratedData,
              id: rawData.id, // ensure original id is preserved
            }),
          ),
      ),
      Effect.flatMap((vendor) =>
        pipe(
          vendor.validateInvariants(),
          Effect.map(() => vendor),
        ),
      ),
    );
  }

  // --- Business Methods ---

  activate(
    modifiedBy: string,
  ): Effect.Effect<Vendor, VendorInvariantViolationError> {
    if (this.data.isActive) {
      return Effect.fail(
        new VendorInvariantViolationError({
          vendorId: this.data.id,
          reason: 'Cannot activate a vendor that is already active',
        }),
      );
    }
    return Effect.succeed(
      new Vendor(
        VendorAggregateState({
          ...this.data,
          isActive: true,
          modifiedBy,
          // `updatedAt` is no longer set here; the database trigger will handle it.
        }),
      ),
    );
  }

  deactivate(
    modifiedBy: string,
  ): Effect.Effect<Vendor, VendorInvariantViolationError> {
    if (!this.data.isActive) {
      return Effect.fail(
        new VendorInvariantViolationError({
          vendorId: this.data.id,
          reason: 'Cannot deactivate a vendor that is already inactive',
        }),
      );
    }
    return Effect.succeed(
      new Vendor(
        VendorAggregateState({
          ...this.data,
          isActive: false,
          modifiedBy,
          // `updatedAt` is no longer set here; the database trigger will handle it.
        }),
      ),
    );
  }

  /**
   * Updates the vendor with new data following the "replace all" pattern.
   * All translations are replaced atomically.
   */
  update(props: {
    isActive: boolean;
    startDate?: string | null;
    endDate?: string | null;
    modifiedBy: string;
    translations: VendorTranslationCollection;
  }): Effect.Effect<Vendor, VendorInvariantViolationError> {
    const newVendor = new Vendor(
      VendorAggregateState({
        ...this.data,
        isActive: props.isActive,
        startDate: props.startDate ?? null,
        endDate: props.endDate ?? null,
        modifiedBy: props.modifiedBy,
        translations: props.translations,
        // `updatedAt` is no longer set here; the database trigger will handle it.
      }),
    );

    return pipe(
      newVendor.validateInvariants(),
      Effect.map(() => newVendor),
    );
  }

  // --- Query Methods ---

  get id(): string {
    return this.data.id;
  }

  /**
   * Gets a composite view of all translatable fields, with locale fallbacks.
   * Delegates the complex logic to the VendorTranslationCollection.
   */
  getCompositeTranslations(locale: string, fallbackLocale = 'en') {
    return this.data.translations.getCompositeTranslations(
      locale,
      fallbackLocale,
    );
  }

  /**
   * Gets the entire collection of VendorTranslation value objects.
   * Useful for views that need to display all available translations, like an edit form.
   */
  getAllTranslations(): readonly VendorTranslation[] {
    return this.data.translations.getAll();
  }

  isActive(): boolean {
    return this.data.isActive ?? true;
  }

  getStartDate(): string | null {
    return this.data.startDate;
  }

  getEndDate(): string | null {
    return this.data.endDate;
  }

  getModifiedBy(): string | null {
    return this.data.modifiedBy;
  }

  getCreatedAt(): string | undefined {
    return this.data.createdAt;
  }

  getUpdatedAt(): string | undefined {
    return this.data.updatedAt;
  }

  // --- Persistence Methods ---

  toRaw(): Effect.Effect<VendorData, ParseResult.ParseError> {
    const SourceVendorSchema = Schema.extend(
      DbVendorDataSchema.omit('createdAt', 'updatedAt'),
      Schema.Struct({
        createdAt: Schema.optional(Schema.String),
        updatedAt: Schema.optional(Schema.String),
      }),
    );

    // Create the failable transformer
    const transformer = Schema.transformOrFail(
      SourceVendorSchema,
      DbVendorDataSchema,
      {
        decode: (source, _, ast) => {
          if (source.createdAt && source.updatedAt) {
            // The cast is safe because we have checked the properties exist.
            return ParseResult.succeed(source as VendorData);
          }
          return ParseResult.fail(
            new ParseResult.Type(
              ast,
              source,
              'Cannot serialize for persistence because createdAt or updatedAt is missing',
            ),
          );
        },
        encode: (val, _options, ast) =>
          ParseResult.fail(
            new ParseResult.Forbidden(ast, val, 'Encoding is not supported'),
          ),
      },
    );

    const vendorData = R.omit(this.data, ['translations']);

    return pipe(
      this.data.translations.toPersistenceArray(),
      Effect.flatMap((persistableTranslations) =>
        Schema.decode(transformer)({
          ...vendorData,
          translations: persistableTranslations,
        }),
      ),
    );
  }

  // --- Invariant Validation ---

  private validateInvariants(): Effect.Effect<
    void,
    VendorInvariantViolationError
  > {
    // Check that vendor has at least one translation
    if (this.data.translations.isEmpty()) {
      return Effect.fail(
        new VendorInvariantViolationError({
          vendorId: this.data.id,
          reason: 'Vendor must have at least one translation',
        }),
      );
    }

    // Check that at least one translation has a name
    const hasValidName = this.data.translations.getAll().some((t) => {
      const name = t.getName();
      return name !== null && name !== undefined && name.trim() !== '';
    });

    if (!hasValidName) {
      return Effect.fail(
        new VendorInvariantViolationError({
          vendorId: this.data.id,
          reason: 'Vendor must have at least one translation with a valid name',
        }),
      );
    }

    // Business rule: If endDate is provided, it must be after startDate
    if (this.data.startDate && this.data.endDate) {
      const startDate = new Date(this.data.startDate);
      const endDate = new Date(this.data.endDate);
      if (endDate <= startDate) {
        return Effect.fail(
          new VendorInvariantViolationError({
            vendorId: this.data.id,
            reason: 'End date must be after start date',
          }),
        );
      }
    }

    return Effect.succeed(void 0);
  }
}
