'use client';

import { unitsColumns } from '@/app/[locale]/bottin/unites/columns';
import { DirectoryList } from '@/components/directory-list/directory-list';
import { initialColumnVisibility } from '@/constants/directory/units';
import type { SupportedLocale } from '@/types/locale';
import { DbUnit } from '@rie/db-schema/entity-types';

type UnitsListProps = {
  locale: SupportedLocale;
};

export const UnitsList = ({ locale }: UnitsListProps) => {
  return (
    <DirectoryList<'unit', 'list', DbUnit, Record<string, unknown>>
      directoryListKey="unit"
      view="list"
      locale={locale}
      columns={unitsColumns()}
      initialColumnVisibility={initialColumnVisibility}
      resourceName="units"
    />
  );
};
