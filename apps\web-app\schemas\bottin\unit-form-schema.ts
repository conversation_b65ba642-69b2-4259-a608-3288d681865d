import { selectOptionSchema } from '@/schemas/common-schema';
import { z } from 'zod';

export const getUnitFormSchema = (
  t: (val: string, args?: Record<string, number | string>) => string,
) => {
  const nameSchema = z
    .array(
      z.object({
        locale: z.string().trim(),
        value: z
          .string()
          .trim()
          .max(250, {
            message: t('name.error.max', { max: 250 }),
          }),
      }),
    )
    .refine((data) => data.some(({ value }) => value !== ''), {
      message: 'name.error.required',
    });

  const aliasSchema = z.array(
    z.object({
      locale: z.string().trim(),
      value: z
        .string()
        .trim()
        .max(1000, {
          message: t('alias.error.max', {
            max: 1000,
          }),
        })
        .optional(),
    }),
  );

  const pseudonymSchema = z.array(
    z.object({
      locale: z.string().trim(),
      value: z
        .string()
        .trim()
        .max(150, {
          message: t('acronym.error.max', { max: 1000 }),
        }),
    }),
  );

  const parentUnit = selectOptionSchema(t('parentUnit.error.required'));

  return z.object({
    alias: aliasSchema,
    id: z.string().optional(),
    names: nameSchema,
    parentUnit: parentUnit,
    pseudonym: pseudonymSchema,
    relatedOrganizations: z.array(z.string().trim().optional()),
    unitType: z.string().default('1'),
  });
};

export type UnitFormSchema = z.infer<ReturnType<typeof getUnitFormSchema>>;
