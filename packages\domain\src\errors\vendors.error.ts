import * as Data from 'effect/Data';

/**
 * Error thrown when attempting to create a vendor that already exists
 */
export class VendorAlreadyExistsError extends Data.TaggedError(
  'VendorAlreadyExistsError',
)<{
  readonly name: string;
}> { }

/**
 * Error thrown when a vendor cannot be found
 */
export class VendorNotFoundError extends Data.TaggedError(
  'VendorNotFoundError',
)<{
  readonly id?: string;
  readonly name?: string;
}> { }

/**
 * Error thrown when vendor validation fails
 */
export class VendorValidationError extends Data.TaggedError(
  'VendorValidationError',
)<{
  readonly message: string;
  readonly fields?: Record<string, string>;
}> { }

/**
 * Error thrown when vendor business invariants are violated
 */
export class VendorInvariantViolationError extends Data.TaggedError(
  'VendorInvariantViolationError',
)<{
  readonly vendorId: string;
  readonly reason: string;
}> { }

/**
 * Error thrown when vendor persistence operations fail
 */
export class VendorPersistenceError extends Data.TaggedError(
  'VendorPersistenceError',
)<{
  readonly vendorId?: string;
  readonly reason: string;
}> { }
