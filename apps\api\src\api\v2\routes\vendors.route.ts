import { handleEffectError } from '@/api/v2/utils/error-handler';
import { VendorsRuntime } from '@/infrastructure/runtimes/vendors.runtime';
import { HonoVariables } from '@/types/common.type';
import {
  SelectOptionDtoSchema,
  VendorDetailResponseDtoSchema,
  VendorEditResponseDtoSchema,
  VendorFormRequestDtoSchema,
  VendorListItemDtoSchema,
} from '@rie/api-contracts';
import {
  CollectionViewParamSchema,
  ResourceIdSchema,
  ResourceViewSchema,
  UserIdSchema,
} from '@rie/domain/schemas';
import { VendorsServiceLive } from '@rie/services';
import { checkPermission, CurrentUser, withPolicy } from '@rie/services/policies';
import { Effect } from 'effect';
import * as Exit from 'effect/Exit';
import * as Schema from 'effect/Schema';
import { Hono } from 'hono';
import { describeRoute } from 'hono-openapi';
import { resolver, validator } from 'hono-openapi/effect';

export const getAllVendorsRoute = describeRoute({
  description: 'Lister tous les manufacturiers',
  operationId: 'getAllVendors',
  parameters: [
    {
      name: 'view',
      in: 'query',
      required: true,
      schema: resolver(CollectionViewParamSchema),
      description: 'Type de vue pour la collection (list ou select)',
    },
    {
      name: 'locale',
      in: 'query',
      required: false,
      schema: resolver(Schema.String),
      description: 'Locale for response translations (e.g., fr, en)',
    },
    {
      name: 'fallbackLocale',
      in: 'query',
      required: false,
      schema: resolver(Schema.String),
      description: 'Fallback locale for response translations (e.g., fr, en)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Union(
              Schema.Array(VendorListItemDtoSchema),
              Schema.Array(SelectOptionDtoSchema),
            ),
          ),
        },
      },
      description: 'Manufacturiers retournés',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Vendors'],
});

export const getVendorByIdRoute = describeRoute({
  description: 'Obtenir un manufacturier par ID',
  operationId: 'getVendorById',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du manufacturier',
    },
    {
      name: 'view',
      in: 'query',
      required: true,
      schema: resolver(ResourceViewSchema),
      description: 'Type de vue pour la ressource (detail ou edit)',
    },
    {
      name: 'locale',
      in: 'query',
      required: false,
      schema: resolver(Schema.String),
      description: 'Locale for response translations (e.g., fr, en)',
    },
    {
      name: 'fallbackLocale',
      in: 'query',
      required: false,
      schema: resolver(Schema.String),
      description: 'Fallback locale for response translations (e.g., fr, en)',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Union(
              VendorDetailResponseDtoSchema,
              VendorEditResponseDtoSchema,
            ),
          ),
        },
      },
      description: 'Manufacturier retourné',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Manufacturier non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Vendors'],
});

export const createVendorRoute = describeRoute({
  description: 'Créer un nouveau manufacturier',
  operationId: 'createVendor',
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(VendorFormRequestDtoSchema),
        example: {
          name: [
            { locale: 'en', value: 'Acme Corporation' },
            { locale: 'fr', value: 'Corporation Acme' },
          ],
          alias: [
            { locale: 'en', value: 'ACME' },
            { locale: 'fr', value: 'ACME' },
          ],
          description: [
            { locale: 'en', value: 'Leading technology company' },
            { locale: 'fr', value: 'Entreprise technologique de premier plan' },
          ],
          otherNames: [
            { locale: 'en', value: 'Acme Tech, Acme Solutions' },
            { locale: 'fr', value: 'Acme Tech, Solutions Acme' },
          ],
          website: 'https://www.acme.com',
          startDate: '2020-01-01',
          endDate: null,
          phones: [],
          contacts: [],
        },
      },
    },
  },
  responses: {
    201: {
      content: {
        'application/json': {
          schema: resolver(VendorEditResponseDtoSchema),
        },
      },
      description: 'Manufacturier créé',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Données invalides',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Vendors'],
});

export const updateVendorRoute = describeRoute({
  description: 'Mettre à jour un manufacturier',
  operationId: 'updateVendor',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du manufacturier à mettre à jour',
    },
  ],
  requestBody: {
    required: true,
    content: {
      'application/json': {
        schema: resolver(VendorFormRequestDtoSchema),
        example: {
          name: [
            { locale: 'en', value: 'Updated Acme Corporation' },
            { locale: 'fr', value: 'Corporation Acme Mise à Jour' },
          ],
          alias: [
            { locale: 'en', value: 'ACME' },
            { locale: 'fr', value: 'ACME' },
          ],
          description: [
            { locale: 'en', value: 'Updated leading technology company' },
            { locale: 'fr', value: 'Entreprise technologique de premier plan mise à jour' },
          ],
          otherNames: [
            { locale: 'en', value: 'Acme Tech, Acme Solutions' },
            { locale: 'fr', value: 'Acme Tech, Solutions Acme' },
          ],
          website: 'https://www.acme.com',
          startDate: '2020-01-01',
          endDate: null,
          phones: [],
          contacts: [],
        },
      },
    },
  },
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(VendorEditResponseDtoSchema),
        },
      },
      description: 'Manufacturier mis à jour',
    },
    400: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Données invalides',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Manufacturier non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Vendors'],
});

export const deleteVendorRoute = describeRoute({
  description: 'Supprimer un manufacturier',
  operationId: 'deleteVendor',
  parameters: [
    {
      name: 'id',
      in: 'path',
      required: true,
      schema: resolver(ResourceIdSchema),
      description: 'ID du manufacturier à supprimer',
    },
  ],
  responses: {
    200: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({ success: Schema.Boolean, message: Schema.String }),
          ),
        },
      },
      description: 'Manufacturier supprimé',
    },
    401: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Non autorisé',
    },
    404: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Manufacturier non trouvé',
    },
    500: {
      content: {
        'application/json': {
          schema: resolver(
            Schema.Struct({
              error: Schema.String,
            }),
          ),
        },
      },
      description: 'Erreur interne du serveur',
    },
  },
  tags: ['Vendors'],
});

const vendorsRoute = new Hono<{
  Variables: HonoVariables;
}>();

vendorsRoute.get(
  '/',
  getAllVendorsRoute,
  validator(
    'query',
    Schema.Struct({
      locale: Schema.optional(Schema.String),
      fallbackLocale: Schema.optional(Schema.String),
      view: Schema.Union(Schema.Literal('list'), Schema.Literal('select')),
    }),
  ),
  async (ctx) => {
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const { locale = 'fr', fallbackLocale = 'fr', view } = ctx.req.valid('query');

    const program = Effect.gen(function* () {
      const vendorService = yield* VendorsServiceLive;
      return yield* vendorService.getAllVendors({
        locale,
        fallbackLocale,
        view,
      });
    }).pipe(
      withPolicy(checkPermission('vendor:read')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    console.log('[Route][Vendors][GET /] view=%s locale=%s fallback=%s', view, locale, fallbackLocale);
    const result = await VendorsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      console.error('[Route][Vendors][GET /] errorResponse returned');
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      console.log('[Route][Vendors][GET /] success count=%d', Array.isArray(result.value) ? result.value.length : -1);
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

vendorsRoute.get(
  '/:id',
  getVendorByIdRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  validator(
    'query',
    Schema.Struct({
      locale: Schema.optional(Schema.String),
      fallbackLocale: Schema.optional(Schema.String),
      view: Schema.Union(Schema.Literal('detail'), Schema.Literal('edit')),
    }),
  ),
  async (ctx) => {
    const id = ctx.req.param('id');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const { locale = 'fr', fallbackLocale = 'fr', view } = ctx.req.valid('query');

    const program = Effect.gen(function* () {
      const vendorService = yield* VendorsServiceLive;
      return yield* vendorService.getVendorById({
        id,
        locale,
        fallbackLocale,
        view,
      });
    }).pipe(
      withPolicy(checkPermission('vendor:read')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    const result = await VendorsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }
    if (Exit.isSuccess(result)) {
      return ctx.json(result.value);
    }
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

vendorsRoute.post(
  '/',
  createVendorRoute,
  async (ctx) => {
    const body = await ctx.req.json();
    const user = ctx.get('user');
    const session = ctx.get('session');

    console.log('[Route][Vendors][POST] Parsed body after validation:', JSON.stringify(body, null, 2));
    console.log('[Route][Vendors][POST] User ID:', user?.id);

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      console.log('[Route][Vendors][POST] Calling vendor service...');
      const vendorService = yield* VendorsServiceLive;
      console.log('[Route][Vendors][POST] Vendor service obtained, calling createVendor...');
      const result = yield* vendorService.createVendor({
        vendorDto: body,
        userId: user.id,
      });
      console.log('[Route][Vendors][POST] Service call completed successfully');
      return result;
    }).pipe(
      withPolicy(checkPermission('vendor:create')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    console.log('[Route][Vendors][POST] Running program...');
    const result = await VendorsRuntime.runPromiseExit(program);
    console.log('[Route][Vendors][POST] Program execution completed');

    if (Exit.isFailure(result)) {
      console.error('[Route][Vendors][POST] Program failed with error:', result.cause);
    }

    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      console.log('[Route][Vendors][POST] Returning error response');
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      console.log('[Route][Vendors][POST] Success! Returning result');
      return ctx.json(result.value, 201);
    }

    console.log('[Route][Vendors][POST] Unexpected case - returning 500');
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

vendorsRoute.put(
  '/:id',
  updateVendorRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx, next) => {
    try {
      const cloned = ctx.req.raw.clone();
      const bodyText = await cloned.text();
      console.log('[Route][Vendors][PUT] Raw body:', bodyText);
    } catch (e) {
      console.error('[Route][Vendors][PUT] Failed to read raw body', e);
    }
    await next();
  },
  async (ctx) => {
    const id = ctx.req.param('id');
    const body = await ctx.req.json();
    const user = ctx.get('user');
    const session = ctx.get('session');

    console.log('[Route][Vendors][PUT] Parsed body after validation:', JSON.stringify(body, null, 2));
    console.log('[Route][Vendors][PUT] Vendor ID:', id);
    console.log('[Route][Vendors][PUT] User ID:', user?.id);

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      console.log('[Route][Vendors][PUT] Calling vendor service...');
      const vendorService = yield* VendorsServiceLive;
      console.log('[Route][Vendors][PUT] Vendor service obtained, calling updateVendor...');
      const result = yield* vendorService.updateVendor({
        id,
        vendorDto: body,
        userId: user.id,
      });
      console.log('[Route][Vendors][PUT] Service call completed successfully');
      return result;
    }).pipe(
      withPolicy(checkPermission('vendor:update')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    console.log('[Route][Vendors][PUT] Running program...');
    const result = await VendorsRuntime.runPromiseExit(program);
    console.log('[Route][Vendors][PUT] Program execution completed');

    if (Exit.isFailure(result)) {
      console.error('[Route][Vendors][PUT] Program failed with error:', result.cause);
    }

    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      console.log('[Route][Vendors][PUT] Returning error response');
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      console.log('[Route][Vendors][PUT] Success! Returning result');
      return ctx.json(result.value);
    }

    console.log('[Route][Vendors][PUT] Unexpected case - returning 500');
    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

vendorsRoute.delete(
  '/:id',
  deleteVendorRoute,
  validator('param', Schema.Struct({ id: ResourceIdSchema })),
  async (ctx) => {
    const id = ctx.req.param('id');
    const user = ctx.get('user');
    const session = ctx.get('session');

    if (!user || !session) {
      return ctx.json({ error: 'Unauthorized' }, 401);
    }

    const program = Effect.gen(function* () {
      const vendorService = yield* VendorsServiceLive;
      return yield* vendorService.deleteVendor(id);
    }).pipe(
      withPolicy(checkPermission('vendor:delete')),
      Effect.provideService(CurrentUser, {
        sessionId: session.id,
        userId: UserIdSchema.make(user.id),
      }),
    );
    const result = await VendorsRuntime.runPromiseExit(program);
    const errorResponse = handleEffectError(ctx, result);
    if (errorResponse) {
      return errorResponse;
    }

    if (Exit.isSuccess(result)) {
      return ctx.json({ success: true, message: 'Vendor deleted' });
    }

    return ctx.json({ error: 'An error occurred' }, 500);
  },
);

export { vendorsRoute };
