'use client';

import { Header<PERSON>enderer } from '@/components/header-renderer/header-renderer';
import { DbUnit } from '@rie/db-schema/entity-types';
import type { ColumnDef } from '@tanstack/react-table';
import { useFormatter } from 'next-intl';
import { DirectoryEntityActions } from '../directory-page/directory-entity-actions';

export const unitsColumns = (): ColumnDef<DbUnit>[] => {
  const directoryEntity = 'unit' as const;

  return [
    {
      accessorKey: 'name',
      cell: (cell) => cell.getValue(),
      enableHiding: false,
      enablePinning: true,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.name"
        />
      ),
      id: 'name',
      maxSize: 400,
      minSize: 250,
    },
    {
      accessorKey: 'acronym',
      cell: (cell) => cell.getValue() || '-',
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.acronym"
        />
      ),
      id: 'acronym',
      maxSize: 200,
      minSize: 150,
    },
    {
      accessorKey: 'parentName',
      cell: (cell) => cell.getValue() || '-',
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.parentName"
        />
      ),
      id: 'parentName',
      maxSize: 300,
      minSize: 200,
    },
    {
      accessorKey: 'lastUpdatedAt',
      cell: ({ cell }) => {
        const format = useFormatter();
        const date = new Date(cell.getValue() as string);
        const formattedDate = cell.getValue()
          ? format.dateTime(date, {
              day: '2-digit',
              month: '2-digit',
              year: 'numeric',
            })
          : '-';

        return (
          <div className="flex items-center">
            <span>{formattedDate}</span>
          </div>
        );
      },
      enableHiding: true,
      enablePinning: false,
      header: ({ column: _column }) => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.lastUpdatedAt"
        />
      ),
      id: 'lastUpdatedAt',
      maxSize: 200,
      minSize: 150,
    },
    {
      accessorKey: 'actions',
      enableHiding: false,
      enablePinning: false,
      header: () => (
        <HeaderRenderer
          namespace={`directory.${directoryEntity}`}
          translationKey="table.columns.actions"
        />
      ),
      cell: ({ row }) => (
        <DirectoryEntityActions row={row} directoryEntity={directoryEntity} />
      ),
      id: 'actions',
      size: 105,
    },
  ];
};
