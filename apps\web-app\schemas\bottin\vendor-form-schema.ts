import { civicCampusAddressSchema } from '@/schemas/common-schema';
import { z } from 'zod';

export const getVendorFormSchema = (
  t: (val: string, args?: Record<string, number | string>) => string,
) => {
  const nameSchema = z
    .array(
      z.object({
        locale: z.string().trim(),
        value: z
          .string()
          .trim()
          .max(150, {
            message: t('description.generalInfo.fields.name.error.max', {
              max: 150,
            }),
          }),
      }),
    )
    .refine((data) => data.some(({ value }) => value !== ''), {
      message: t('description.generalInfo.fields.name.error.required'),
    });

  const aliasSchema = z.array(
    z.object({
      locale: z.string().trim(),
      value: z
        .string()
        .trim()
        .max(1000, {
          message: t('description.generalInfo.fields.alias.error.max', {
            max: 1000,
          }),
        }),
    }),
  );

  const descriptionFieldSchema = z.array(
    z.object({
      locale: z.string().trim(),
      value: z
        .string()
        .trim()
        .max(2000, {
          message: t('description.generalInfo.fields.description.error.max', {
            max: 2000,
          }),
        }),
    }),
  );

  const datesSchema = z.date().nullable();

  const generalInfoSchema = z.object({
    alias: aliasSchema,
    dateEnd: datesSchema,
    name: nameSchema,
  });

  const phone = z.object({
    description: descriptionFieldSchema,
    phone: z
      .string()
      .trim()
      .max(15, {
        message: t('description.contactDetails.fields.phone.error.max', {
          max: 15,
        }),
      })
      .optional(),
  });

  return generalInfoSchema.extend({
    id: z.string().optional(),
    contacts: z.array(civicCampusAddressSchema()),
    phones: z.array(phone),
  });
};

export type VendorFormSchema = z.infer<
  ReturnType<typeof getVendorFormSchema>
>;
