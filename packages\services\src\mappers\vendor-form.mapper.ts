import { VendorFormRequestDtoSchema } from '@rie/api-contracts';
import {
  DbVendorI18NInputSchema,
  VendorInputSchema,
} from '@rie/domain/schemas';
import type { VendorI18NInput, Writable } from '@rie/domain/types';
import * as ParseResult from 'effect/ParseResult';
import * as Schema from 'effect/Schema';

export const VendorFormToDomainInput = Schema.transformOrFail(
  VendorFormRequestDtoSchema,
  VendorInputSchema,
  {
    strict: true,
    decode: (formDto, _, ast) => {
      return ParseResult.try({
        try: () => {
          const translationsMap = new Map<string, Writable<VendorI18NInput>>();

          const ensureLocale = (locale: string) => {
            if (!translationsMap.has(locale)) {
              translationsMap.set(locale, {
                locale,
                name: null,
                website: null,
                description: null,
                otherNames: null,
              });
            }
          };

          for (const translation of formDto.translations) {
            ensureLocale(translation.locale);
            const itemFound = translationsMap.get(translation.locale);
            if (itemFound) {
              itemFound.name = translation.name;
              itemFound.website = translation.website || null;
              itemFound.description = translation.description || null;
              itemFound.otherNames = translation.otherNames || null;
            }
          }

          const translations = Array.from(translationsMap.values()).map((t) =>
            Schema.decodeSync(DbVendorI18NInputSchema)(t),
          );

          return {
            isActive: formDto.isActive ?? true, // Default to true for new vendors
            startDate: formDto.startDate ?? null,
            endDate: formDto.endDate ?? null,
            translations: translations,
          };
        },
        catch: (error) =>
          new ParseResult.Type(ast, formDto, (error as Error).message),
      });
    },
    encode: (domainInput, _, ast) =>
      ParseResult.fail(
        new ParseResult.Forbidden(
          ast,
          domainInput,
          'Encoding from domain to form is not supported by this transformer',
        ),
      ),
  },
);
