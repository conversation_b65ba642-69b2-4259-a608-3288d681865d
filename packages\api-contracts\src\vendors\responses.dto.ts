import * as Schema from 'effect/Schema';
import {
  OptionalLocalizedFieldDtoSchema,
  RequiredLocalizedFieldDtoSchema,
} from '../common';

// Base schema for vendors
export const VendorBaseDtoSchema = Schema.Struct({
  id: Schema.String,
  isActive: Schema.Boolean,
  startDate: Schema.NullOr(Schema.String),
  endDate: Schema.NullOr(Schema.String),
});

// DTO for lists of vendors (e.g., view='list')
export const VendorListItemDtoSchema = Schema.Struct({
  id: Schema.String,
  name: Schema.String,
  dateEnd: Schema.NullOr(Schema.String),
  lastUpdatedAt: Schema.String,
  startDate: Schema.NullOr(Schema.String),
  endDate: Schema.NullOr(Schema.String),
});

export type VendorListItemDTO = Schema.Schema.Type<
  typeof VendorListItemDtoSchema
>;

// Vendor-specific translation schemas
export const VendorTranslationDtoSchema = Schema.Struct({
  name: RequiredLocalizedFieldDtoSchema,
  website: OptionalLocalizedFieldDtoSchema,
  description: OptionalLocalizedFieldDtoSchema,
  otherNames: OptionalLocalizedFieldDtoSchema,
});

export const VendorTranslationInputDtoSchema = Schema.Struct({
  name: OptionalLocalizedFieldDtoSchema,
  website: OptionalLocalizedFieldDtoSchema,
  description: OptionalLocalizedFieldDtoSchema,
  otherNames: OptionalLocalizedFieldDtoSchema,
});

// DTO for the detailed view of a single vendor (e.g., view='detail')
export const VendorDetailResponseDtoSchema = Schema.extend(
  VendorBaseDtoSchema,
  Schema.Struct({
    translations: VendorTranslationDtoSchema,
  }),
);

export type VendorDetailResponseDTO = Schema.Schema.Type<
  typeof VendorDetailResponseDtoSchema
>;

// DTO for vendor selection (e.g., view='select')
export const VendorSelectResponseDtoSchema = Schema.Struct({
  value: Schema.String,
  label: Schema.String,
});

export type VendorSelectResponseDTO = Schema.Schema.Type<
  typeof VendorSelectResponseDtoSchema
>;

// DTO for vendor edit view
export const VendorEditResponseDtoSchema = Schema.Struct({
  id: Schema.String,
  startDate: Schema.NullOr(Schema.String),
  endDate: Schema.NullOr(Schema.String),
  translations: Schema.Array(
    Schema.Struct({
      locale: Schema.String,
      name: Schema.String,
      website: Schema.optional(Schema.String),
      description: Schema.optional(Schema.String),
      otherNames: Schema.optional(Schema.String),
    }),
  ),
});

export type VendorEditResponseDTO = Schema.Schema.Type<
  typeof VendorEditResponseDtoSchema
>;
