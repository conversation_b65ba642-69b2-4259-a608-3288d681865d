// Note: This file was previously entirely commented out.
// Providing a basic structure - full mock data may need to be restored if needed.

export const equipmentForServiceContractMocks: any[] = [
    // Equipment mock data would go here
    // The original file contained extensive mock data that was commented out
    // If needed, the commented data below can be restored and properly typed
];

export const equipementMocks: any[] = [
    // Equipment mock data would go here
];

// Original commented data preserved below for reference:
// import { EquipmentData, EquipmentFull } from '@/types/equipment';
// export const equipmentForServiceContractMocks: EquipmentFull[] = [
//   {
//     _class: 'Rie\\RieCoreBundle\\Entity\\Equipement',
//     _className: 'Equipement',
//     _resourceName: 'equipement',
//     anneeFabrication: null,
//     batimentLocaux: [],
//     campusLocaux: [],
//     categories: [],
//     commentaire: null,
//     commentaires: {
//       en: null,
//       fr: null,
//     },
//     contratServices: [],
//     coutEspece: '361014.00',
//     coutNature: null,
//     coutTotal: 361014,
//     createdAt: '2023-10-17T06:11:32-0400',
//     dateAchat: null,
//     dateDecommission: null,
//     dateFin: null,
//     dateInstallation: null,
//     dateInstalOuAchat: null,
//     defaillance: null,
//     defaillances: {
//       en: null,
//       fr: null,
//     },
//     description: null,
//     descriptions: {
//       en: null,
//       fr: null,
//     },
//     detaillants: [],
//     disposition: null,
//     dispositions: {
//       en: null,
//       fr: null,
//     },
//     documents: [],
//     doi: null,
//     domaineRecherches: [],
//     emplacement: null,
//     equipementAccessoires: [],
//     equipementAutres: [],
//     equipementComposantes: [],
//     equipementDimension: null,
//     equipementDureeVie: null,
//     equipementEmplacement: null,
//     equipementId: 54,
//     equipementLogiciels: [],
//     equipementParents: [],
//     estEssais: false,
//     estMasquer: false,
//     etatEquipement: {
//       descriptions: {
//         fr: null,
//       },
//       id: 1,
//       nom: 'Fonctionnel',
//       noms: {
//         fr: 'Fonctionnel',
//       },
//       slug: 'fonctionnel',
//       text: 'Fonctionnel',
//       uid: 'fonctionnel',
//     },
//     financement: null,
//     financements: {
//       en: null,
//       fr: null,
//     },
//     fournisseur: null,
//     frequenceMaintenance: null,
//     id: 'E000000058',
//     ID: 'equipement.E000000058',
//     infrastructure: {
//       acronyme: 'IRIC Biologie in vivo',
//       acronymes: {
//         fr: 'IRIC Biologie in vivo',
//       },
//       batimentLocaux: [],
//       campusLocaux: [],
//       createdAt: '2023-10-17T05:54:30-0400',
//       descriptions: {
//         fr: 'L’institut héberge une des plus grandes animaleries au Canada offrant un secteur exempt de pathogènes, un secteur conventionnel et une quarantaine autonome. En plus de l’hébergement et des supports technique et vétérinaire pour la recherche et la santé animale, l’animalerie de l’institut collabore avec la plateforme de biopharmacie de la Faculté de pharmacie de l’Université de Montréal pour offrir des services de pharmacocinétique, bioanalyse et toxicologie (www.pfbio.ca) .',
//       },
//       directeursTechnique: [
//         {
//           created_at: '2023-10-16T18:02:58-0400',
//           emails: [
//             {
//               address: '<EMAIL>',
//               id: 678,
//               text: '<EMAIL>',
//             },
//           ],
//           family_name: 'Sauvageau',
//           given_name: 'Guy',
//           id: '652db312ee859',
//           last_updated_at: '2023-10-16T18:02:58-0400',
//           name_with_lastname_first: 'Sauvageau, Guy',
//           person_id: 765,
//           text: 'Guy Sauvageau',
//           type_contenu: 'Person',
//           uid: 'in14491',
//         },
//       ],
//       emplacement: null,
//       id: 'I000018',
//       infrastructureId: 271,
//       infrastructureReception: null,
//       juridiction: {
//         acronym: 'IRIC',
//         acronyms: {
//           fr: 'IRIC',
//         },
//         createdAt: '2023-10-16T21:56:50-0400',
//         dateBegin: null,
//         dateEnd: null,
//         descriptions: {
//           fr: null,
//         },
//         id: 'A000017',
//         lastUpdatedAt: '2023-10-16T17:59:58-0400',
//         names: {
//           fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//         },
//         organizationId: 10,
//         parent: {
//           acronym: null,
//           acronyms: {
//             en: null,
//             fr: null,
//           },
//           createdAt: '2023-10-16T21:48:30-0400',
//           dateBegin: null,
//           dateEnd: null,
//           descriptions: {
//             en: null,
//             fr: null,
//           },
//           id: 'G000001',
//           lastUpdatedAt: '2023-10-16T21:48:30-0400',
//           names: {
//             en: 'University of Montreal',
//             fr: 'Université de Montréal',
//           },
//           organizationId: 1,
//           parent: null,
//           pseudonymes: {
//             en: null,
//             fr: null,
//           },
//           text: 'Université de Montréal',
//           typeContenu: 'Etablissement',
//           typeEtablissement: {
//             descriptions: {
//               fr: null,
//             },
//             id: 1,
//             nom: 'Académique',
//             noms: {
//               fr: 'Académique',
//             },
//             slug: 'academique',
//             text: 'Académique',
//             uid: 'academique',
//           },
//           uid: 'G000001',
//         },
//         pseudonymes: {
//           fr: null,
//         },
//         text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//         typeContenu: 'Unite',
//         uid: 'ur13772',
//       },
//       laboratoireInnovations: [],
//       lastUpdatedAt: '2023-10-17T06:35:35-0400',
//       locaux: [],
//       mettreEnVedette: false,
//       nom: "Plateforme de biologie in vivo de l'IRIC",
//       noms: {
//         fr: "Plateforme de biologie in vivo de l'IRIC",
//       },
//       projetFinancements: [],
//       pseudonyme:
//         'Institut de recherche en immunologie et en cancérologie - animalerie. IRIC animalerie',
//       responsablesSST: [],
//       responsablesTechnique: [
//         {
//           created_at: '2023-10-17T05:58:19-0400',
//           emails: [
//             {
//               address: '<EMAIL>',
//               id: 7600,
//               text: '<EMAIL>',
//             },
//           ],
//           family_name: 'Julie',
//           given_name: 'Gervais',
//           id: '652e5abb9f702',
//           last_updated_at: null,
//           name_with_lastname_first: 'Julie, Gervais',
//           person_id: 9221,
//           text: 'Gervais Julie',
//           type_contenu: 'Person',
//           uid: null,
//         },
//       ],
//       statutInfrastructure: {
//         descriptions: {
//           fr: null,
//         },
//         id: 2,
//         nom: 'Actif',
//         noms: {
//           fr: 'Actif',
//         },
//         slug: 'actif',
//         text: 'Actif',
//         uid: 'actif',
//       },
//       telephone: null,
//       text: "Plateforme de biologie in vivo de l'IRIC",
//       totalEquipements: 54,
//       typeContenu: 'Infrastructure',
//       typeInfrastructure: {
//         descriptions: {
//           fr: null,
//         },
//         id: 2,
//         nom: 'Plateforme',
//         noms: {
//           fr: 'Plateforme',
//         },
//         slug: 'plateforme',
//         text: 'Plateforme',
//         uid: 'plateforme',
//       },
//       uid: 'ur14388',
//       unite: {
//         acronym: null,
//         acronyms: {
//           fr: null,
//         },
//         createdAt: '2023-10-16T17:59:15-0400',
//         dateBegin: null,
//         dateEnd: null,
//         descriptions: {
//           fr: null,
//         },
//         id: '652db2331fa8b',
//         lastUpdatedAt: '2023-10-16T17:59:15-0400',
//         names: {
//           fr: 'Faculté de médecine - Département de médecine',
//         },
//         organizationId: 633,
//         parent: {
//           acronym: null,
//           acronyms: {
//             en: null,
//             fr: null,
//           },
//           createdAt: '2023-10-16T21:48:30-0400',
//           dateBegin: null,
//           dateEnd: null,
//           descriptions: {
//             en: null,
//             fr: null,
//           },
//           id: 'G000001',
//           lastUpdatedAt: '2023-10-16T21:48:30-0400',
//           names: {
//             en: 'University of Montreal',
//             fr: 'Université de Montréal',
//           },
//           organizationId: 1,
//           parent: null,
//           pseudonymes: {
//             en: null,
//             fr: null,
//           },
//           text: 'Université de Montréal',
//           typeContenu: 'Etablissement',
//           typeEtablissement: {
//             descriptions: {
//               fr: null,
//             },
//             id: 1,
//             nom: 'Académique',
//             noms: {
//               fr: 'Académique',
//             },
//             slug: 'academique',
//             text: 'Académique',
//             uid: 'academique',
//           },
//           uid: 'G000001',
//         },
//         pseudonymes: {
//           fr: null,
//         },
//         text: 'Faculté de médecine - Département de médecine',
//         typeContenu: 'Unite',
//         uid: 'D203A0',
//       },
//       url: null,
//       visibilite: {
//         descriptions: {
//           fr: null,
//         },
//         id: 1,
//         nom: 'Public',
//         noms: {
//           fr: 'Public',
//         },
//         slug: 'public',
//         text: 'Public',
//         uid: 'public',
//       },
//     },
//     juridiction: {
//       acronym: 'IRIC',
//       acronyms: {
//         fr: 'IRIC',
//       },
//       createdAt: '2023-10-16T21:56:50-0400',
//       dateBegin: null,
//       dateEnd: null,
//       descriptions: {
//         fr: null,
//       },
//       id: 'A000017',
//       lastUpdatedAt: '2023-10-16T17:59:58-0400',
//       names: {
//         fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//       },
//       organizationId: 10,
//       parent: {
//         acronym: null,
//         acronyms: {
//           en: null,
//           fr: null,
//         },
//         createdAt: '2023-10-16T21:48:30-0400',
//         dateBegin: null,
//         dateEnd: null,
//         descriptions: {
//           en: null,
//           fr: null,
//         },
//         id: 'G000001',
//         lastUpdatedAt: '2023-10-16T21:48:30-0400',
//         names: {
//           en: 'University of Montreal',
//           fr: 'Université de Montréal',
//         },
//         organizationId: 1,
//         parent: null,
//         pseudonymes: {
//           en: null,
//           fr: null,
//         },
//         text: 'Université de Montréal',
//         typeContenu: 'Etablissement',
//         typeEtablissement: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Académique',
//           noms: {
//             fr: 'Académique',
//           },
//           slug: 'academique',
//           text: 'Académique',
//           uid: 'academique',
//         },
//         uid: 'G000001',
//       },
//       pseudonymes: {
//         fr: null,
//       },
//       text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//       typeContenu: 'Unite',
//       uid: 'ur13772',
//     },
//     lastUpdatedAt: null,
//     locaux: [],
//     maintenance: null,
//     maintenances: {
//       en: null,
//       fr: null,
//     },
//     manufacturier: null,
//     mettreEnVedette: false,
//     modele: null,
//     nom: 'Autoclave, large (196pi3) for sterilization',
//     noms: {
//       en: 'Autoclave, large (196pi3) for sterilization',
//       fr: 'Autoclave, large (196pi3) for sterilization',
//     },
//     numeroHomologation: null,
//     numeroInventaire: null,
//     numeroSerie: null,
//     photos: [],
//     poleExcellences: [],
//     precisions: [],
//     projetFinancements: [],
//     reparateurs: [],
//     responsablesSST: [],
//     responsablesTechnique: [],
//     risque: null,
//     risques: {
//       en: null,
//       fr: null,
//     },
//     secteurApplications: [],
//     statutDecommissionEquipement: {
//       id: 'date-decommission-indef',
//       noms: {
//         en: 'End of support undefined',
//         fr: 'Fin de support indéterminée',
//       },
//       uid: 'date-decommission-indef',
//     },
//     statutDureeVieEquipement: {
//       id: 'duree-vie-indef',
//       noms: {
//         en: 'Unknown Life expectancy',
//         fr: 'Espérance de vie indéterminée',
//       },
//       uid: 'duree-vie-indef',
//     },
//     techniques: [],
//     text: 'Autoclave, large (196pi3) for sterilization',
//     titulaire: {
//       created_at: '2023-10-16T18:02:58-0400',
//       emails: [
//         {
//           address: '<EMAIL>',
//           id: 678,
//           text: '<EMAIL>',
//         },
//       ],
//       family_name: 'Sauvageau',
//       given_name: 'Guy',
//       id: '652db312ee859',
//       last_updated_at: '2023-10-16T18:02:58-0400',
//       name_with_lastname_first: 'Sauvageau, Guy',
//       person_id: 765,
//       text: 'Guy Sauvageau',
//       type_contenu: 'Person',
//       uid: 'in14491',
//     },
//     typeContenu: 'Equipement',
//     typeEquipement: {
//       descriptions: {
//         fr: null,
//       },
//       id: 1,
//       nom: 'Équipement',
//       noms: {
//         fr: 'Équipement',
//       },
//       slug: 'equipement',
//       text: 'Équipement',
//       uid: 'equipement',
//     },
//     utilisation: null,
//     utilisations: {
//       en: null,
//       fr: null,
//     },
//     videos: [],
//     visibilite: {
//       descriptions: {
//         fr: null,
//       },
//       id: 1,
//       nom: 'Public',
//       noms: {
//         fr: 'Public',
//       },
//       slug: 'public',
//       text: 'Public',
//       uid: 'public',
//     },
//   },
// ];
// export const equipementMocks: EquipmentData[] = [
//   {
//     data: [
//       {
//         _class: 'Rie\\RieCoreBundle\\Entity\\Equipement',
//         _className: 'Equipement',
//         _resourceName: 'equipement',
//         anneeFabrication: null,
//         batimentLocaux: [],
//         campusLocaux: [],
//         categories: [],
//         commentaire: null,
//         commentaires: {
//           en: null,
//           fr: null,
//         },
//         contratServices: [],
//         coutEspece: '2138458.00',
//         coutNature: null,
//         coutTotal: 2138458,
//         createdAt: '2023-10-17T06:11:32-0400',
//         dateAchat: null,
//         dateDecommission: null,
//         dateFin: null,
//         dateInstallation: null,
//         dateInstalOuAchat: null,
//         defaillance: null,
//         defaillances: {
//           en: null,
//           fr: null,
//         },
//         description: null,
//         descriptions: {
//           en: null,
//           fr: null,
//         },
//         detaillants: [],
//         disposition: null,
//         dispositions: {
//           en: null,
//           fr: null,
//         },
//         documents: [],
//         doi: null,
//         domaineRecherches: [],
//         emplacement: null,
//         equipementAccessoires: [],
//         equipementAutres: [],
//         equipementComposantes: [],
//         equipementDimension: null,
//         equipementDureeVie: null,
//         equipementEmplacement: null,
//         equipementId: 49,
//         equipementLogiciels: [],
//         equipementParents: [],
//         estEssais: false,
//         estMasquer: false,
//         etatEquipement: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Fonctionnel',
//           noms: {
//             fr: 'Fonctionnel',
//           },
//           slug: 'fonctionnel',
//           text: 'Fonctionnel',
//           uid: 'fonctionnel',
//         },
//         financement: null,
//         financements: {
//           en: null,
//           fr: null,
//         },
//         fournisseur: null,
//         frequenceMaintenance: null,
//         id: 'E000000053',
//         ID: 'equipement.E000000053',
//         infrastructure: {
//           acronyme: 'IRIC Biologie in vivo',
//           acronymes: {
//             fr: 'IRIC Biologie in vivo',
//           },
//           batimentLocaux: [],
//           campusLocaux: [],
//           createdAt: '2023-10-17T05:54:30-0400',
//           descriptions: {
//             fr: 'L’institut héberge une des plus grandes animaleries au Canada offrant un secteur exempt de pathogènes, un secteur conventionnel et une quarantaine autonome. En plus de l’hébergement et des supports technique et vétérinaire pour la recherche et la santé animale, l’animalerie de l’institut collabore avec la plateforme de biopharmacie de la Faculté de pharmacie de l’Université de Montréal pour offrir des services de pharmacocinétique, bioanalyse et toxicologie (www.pfbio.ca) .',
//           },
//           directeursTechnique: [
//             {
//               created_at: '2023-10-16T18:02:58-0400',
//               emails: [
//                 {
//                   address: '<EMAIL>',
//                   id: 678,
//                   text: '<EMAIL>',
//                 },
//               ],
//               family_name: 'Sauvageau',
//               given_name: 'Guy',
//               id: '652db312ee859',
//               last_updated_at: '2023-10-16T18:02:58-0400',
//               name_with_lastname_first: 'Sauvageau, Guy',
//               person_id: 765,
//               text: 'Guy Sauvageau',
//               type_contenu: 'Person',
//               uid: 'in14491',
//             },
//           ],
//           emplacement: null,
//           id: 'I000018',
//           infrastructureId: 271,
//           infrastructureReception: null,
//           juridiction: {
//             acronym: 'IRIC',
//             acronyms: {
//               fr: 'IRIC',
//             },
//             createdAt: '2023-10-16T21:56:50-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               fr: null,
//             },
//             id: 'A000017',
//             lastUpdatedAt: '2023-10-16T17:59:58-0400',
//             names: {
//               fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//             },
//             organizationId: 10,
//             parent: {
//               acronym: null,
//               acronyms: {
//                 en: null,
//                 fr: null,
//               },
//               createdAt: '2023-10-16T21:48:30-0400',
//               dateBegin: null,
//               dateEnd: null,
//               descriptions: {
//                 en: null,
//                 fr: null,
//               },
//               id: 'G000001',
//               lastUpdatedAt: '2023-10-16T21:48:30-0400',
//               names: {
//                 en: 'University of Montreal',
//                 fr: 'Université de Montréal',
//               },
//               organizationId: 1,
//               parent: null,
//               pseudonymes: {
//                 en: null,
//                 fr: null,
//               },
//               text: 'Université de Montréal',
//               typeContenu: 'Etablissement',
//               typeEtablissement: {
//                 descriptions: {
//                   fr: null,
//                 },
//                 id: 1,
//                 nom: 'Académique',
//                 noms: {
//                   fr: 'Académique',
//                 },
//                 slug: 'academique',
//                 text: 'Académique',
//                 uid: 'academique',
//               },
//               uid: 'G000001',
//             },
//             pseudonymes: {
//               fr: null,
//             },
//             text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//             typeContenu: 'Unite',
//             uid: 'ur13772',
//           },
//           laboratoireInnovations: [],
//           lastUpdatedAt: '2023-10-17T06:35:35-0400',
//           locaux: [],
//           mettreEnVedette: false,
//           nom: "Plateforme de biologie in vivo de l'IRIC",
//           noms: {
//             fr: "Plateforme de biologie in vivo de l'IRIC",
//           },
//           projetFinancements: [],
//           pseudonyme:
//             'Institut de recherche en immunologie et en cancérologie - animalerie. IRIC animalerie',
//           responsablesSST: [],
//           responsablesTechnique: [
//             {
//               created_at: '2023-10-17T05:58:19-0400',
//               emails: [
//                 {
//                   address: '<EMAIL>',
//                   id: 7600,
//                   text: '<EMAIL>',
//                 },
//               ],
//               family_name: 'Julie',
//               given_name: 'Gervais',
//               id: '652e5abb9f702',
//               last_updated_at: null,
//               name_with_lastname_first: 'Julie, Gervais',
//               person_id: 9221,
//               text: 'Gervais Julie',
//               type_contenu: 'Person',
//               uid: null,
//             },
//           ],
//           statutInfrastructure: {
//             descriptions: {
//               fr: null,
//             },
//             id: 2,
//             nom: 'Actif',
//             noms: {
//               fr: 'Actif',
//             },
//             slug: 'actif',
//             text: 'Actif',
//             uid: 'actif',
//           },
//           telephone: null,
//           text: "Plateforme de biologie in vivo de l'IRIC",
//           totalEquipements: 54,
//           typeContenu: 'Infrastructure',
//           typeInfrastructure: {
//             descriptions: {
//               fr: null,
//             },
//             id: 2,
//             nom: 'Plateforme',
//             noms: {
//               fr: 'Plateforme',
//             },
//             slug: 'plateforme',
//             text: 'Plateforme',
//             uid: 'plateforme',
//           },
//           uid: 'ur14388',
//           unite: {
//             acronym: null,
//             acronyms: {
//               fr: null,
//             },
//             createdAt: '2023-10-16T17:59:15-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               fr: null,
//             },
//             id: '652db2331fa8b',
//             lastUpdatedAt: '2023-10-16T17:59:15-0400',
//             names: {
//               fr: 'Faculté de médecine - Département de médecine',
//             },
//             organizationId: 633,
//             parent: {
//               acronym: null,
//               acronyms: {
//                 en: null,
//                 fr: null,
//               },
//               createdAt: '2023-10-16T21:48:30-0400',
//               dateBegin: null,
//               dateEnd: null,
//               descriptions: {
//                 en: null,
//                 fr: null,
//               },
//               id: 'G000001',
//               lastUpdatedAt: '2023-10-16T21:48:30-0400',
//               names: {
//                 en: 'University of Montreal',
//                 fr: 'Université de Montréal',
//               },
//               organizationId: 1,
//               parent: null,
//               pseudonymes: {
//                 en: null,
//                 fr: null,
//               },
//               text: 'Université de Montréal',
//               typeContenu: 'Etablissement',
//               typeEtablissement: {
//                 descriptions: {
//                   fr: null,
//                 },
//                 id: 1,
//                 nom: 'Académique',
//                 noms: {
//                   fr: 'Académique',
//                 },
//                 slug: 'academique',
//                 text: 'Académique',
//                 uid: 'academique',
//               },
//               uid: 'G000001',
//             },
//             pseudonymes: {
//               fr: null,
//             },
//             text: 'Faculté de médecine - Département de médecine',
//             typeContenu: 'Unite',
//             uid: 'D203A0',
//           },
//           url: null,
//           visibilite: {
//             descriptions: {
//               fr: null,
//             },
//             id: 1,
//             nom: 'Public',
//             noms: {
//               fr: 'Public',
//             },
//             slug: 'public',
//             text: 'Public',
//             uid: 'public',
//           },
//         },
//         juridiction: {
//           acronym: 'IRIC',
//           acronyms: {
//             fr: 'IRIC',
//           },
//           createdAt: '2023-10-16T21:56:50-0400',
//           dateBegin: null,
//           dateEnd: null,
//           descriptions: {
//             fr: null,
//           },
//           id: 'A000017',
//           lastUpdatedAt: '2023-10-16T17:59:58-0400',
//           names: {
//             fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//           },
//           organizationId: 10,
//           parent: {
//             acronym: null,
//             acronyms: {
//               en: null,
//               fr: null,
//             },
//             createdAt: '2023-10-16T21:48:30-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               en: null,
//               fr: null,
//             },
//             id: 'G000001',
//             lastUpdatedAt: '2023-10-16T21:48:30-0400',
//             names: {
//               en: 'University of Montreal',
//               fr: 'Université de Montréal',
//             },
//             organizationId: 1,
//             parent: null,
//             pseudonymes: {
//               en: null,
//               fr: null,
//             },
//             text: 'Université de Montréal',
//             typeContenu: 'Etablissement',
//             typeEtablissement: {
//               descriptions: {
//                 fr: null,
//               },
//               id: 1,
//               nom: 'Académique',
//               noms: {
//                 fr: 'Académique',
//               },
//               slug: 'academique',
//               text: 'Académique',
//               uid: 'academique',
//             },
//             uid: 'G000001',
//           },
//           pseudonymes: {
//             fr: null,
//           },
//           text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//           typeContenu: 'Unite',
//           uid: 'ur13772',
//         },
//         lastUpdatedAt: null,
//         locaux: [],
//         maintenance: null,
//         maintenances: {
//           en: null,
//           fr: null,
//         },
//         manufacturier: null,
//         mettreEnVedette: false,
//         modele: null,
//         nom: 'Set of 47 Cage holders for mice (SPF type), 160, vented, automated H2O',
//         noms: {
//           en: 'Set of 47 Cage holders for mice (SPF type), 160, vented, automated H2O',
//           fr: 'Set of 47 Cage holders for mice (SPF type), 160, vented, automated H2O',
//         },
//         numeroHomologation: null,
//         numeroInventaire: null,
//         numeroSerie: null,
//         photos: [],
//         poleExcellences: [],
//         precisions: [],
//         projetFinancements: [],
//         reparateurs: [],
//         responsablesSST: [],
//         responsablesTechnique: [],
//         risque: null,
//         risques: {
//           en: null,
//           fr: null,
//         },
//         secteurApplications: [],
//         statutDecommissionEquipement: {
//           id: 'date-decommission-indef',
//           noms: {
//             en: 'End of support undefined',
//             fr: 'Fin de support indéterminée',
//           },
//           uid: 'date-decommission-indef',
//         },
//         statutDureeVieEquipement: {
//           id: 'duree-vie-indef',
//           noms: {
//             en: 'Unknown Life expectancy',
//             fr: 'Espérance de vie indéterminée',
//           },
//           uid: 'duree-vie-indef',
//         },
//         techniques: [],
//         text: 'Set of 47 Cage holders for mice (SPF type), 160, vented, automated H2O',
//         titulaire: {
//           created_at: '2023-10-16T18:02:58-0400',
//           emails: [
//             {
//               address: '<EMAIL>',
//               id: 678,
//               text: '<EMAIL>',
//             },
//           ],
//           family_name: 'Sauvageau',
//           given_name: 'Guy',
//           id: '652db312ee859',
//           last_updated_at: '2023-10-16T18:02:58-0400',
//           name_with_lastname_first: 'Sauvageau, Guy',
//           person_id: 765,
//           text: 'Guy Sauvageau',
//           type_contenu: 'Person',
//           uid: 'in14491',
//         },
//         typeContenu: 'Equipement',
//         typeEquipement: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Équipement',
//           noms: {
//             fr: 'Équipement',
//           },
//           slug: 'equipement',
//           text: 'Équipement',
//           uid: 'equipement',
//         },
//         utilisation: null,
//         utilisations: {
//           en: null,
//           fr: null,
//         },
//         videos: [],
//         visibilite: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Public',
//           noms: {
//             fr: 'Public',
//           },
//           slug: 'public',
//           text: 'Public',
//           uid: 'public',
//         },
//       },
//       {
//         _class: 'Rie\\RieCoreBundle\\Entity\\Equipement',
//         _className: 'Equipement',
//         _resourceName: 'equipement',
//         anneeFabrication: null,
//         batimentLocaux: [],
//         campusLocaux: [],
//         categories: [],
//         commentaire: null,
//         commentaires: {
//           en: null,
//           fr: null,
//         },
//         contratServices: [],
//         coutEspece: '130009.00',
//         coutNature: null,
//         coutTotal: 130009,
//         createdAt: '2023-10-17T06:11:32-0400',
//         dateAchat: null,
//         dateDecommission: null,
//         dateFin: null,
//         dateInstallation: null,
//         dateInstalOuAchat: null,
//         defaillance: null,
//         defaillances: {
//           en: null,
//           fr: null,
//         },
//         description: null,
//         descriptions: {
//           en: null,
//           fr: null,
//         },
//         detaillants: [],
//         disposition: null,
//         dispositions: {
//           en: null,
//           fr: null,
//         },
//         documents: [],
//         doi: null,
//         domaineRecherches: [],
//         emplacement: null,
//         equipementAccessoires: [],
//         equipementAutres: [],
//         equipementComposantes: [],
//         equipementDimension: null,
//         equipementDureeVie: null,
//         equipementEmplacement: null,
//         equipementId: 50,
//         equipementLogiciels: [],
//         equipementParents: [],
//         estEssais: false,
//         estMasquer: false,
//         etatEquipement: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Fonctionnel',
//           noms: {
//             fr: 'Fonctionnel',
//           },
//           slug: 'fonctionnel',
//           text: 'Fonctionnel',
//           uid: 'fonctionnel',
//         },
//         financement: null,
//         financements: {
//           en: null,
//           fr: null,
//         },
//         fournisseur: null,
//         frequenceMaintenance: null,
//         id: 'E000000054',
//         ID: 'equipement.E000000054',
//         infrastructure: {
//           acronyme: 'IRIC Biologie in vivo',
//           acronymes: {
//             fr: 'IRIC Biologie in vivo',
//           },
//           batimentLocaux: [],
//           campusLocaux: [],
//           createdAt: '2023-10-17T05:54:30-0400',
//           descriptions: {
//             fr: 'L’institut héberge une des plus grandes animaleries au Canada offrant un secteur exempt de pathogènes, un secteur conventionnel et une quarantaine autonome. En plus de l’hébergement et des supports technique et vétérinaire pour la recherche et la santé animale, l’animalerie de l’institut collabore avec la plateforme de biopharmacie de la Faculté de pharmacie de l’Université de Montréal pour offrir des services de pharmacocinétique, bioanalyse et toxicologie (www.pfbio.ca) .',
//           },
//           directeursTechnique: [
//             {
//               created_at: '2023-10-16T18:02:58-0400',
//               emails: [
//                 {
//                   address: '<EMAIL>',
//                   id: 678,
//                   text: '<EMAIL>',
//                 },
//               ],
//               family_name: 'Sauvageau',
//               given_name: 'Guy',
//               id: '652db312ee859',
//               last_updated_at: '2023-10-16T18:02:58-0400',
//               name_with_lastname_first: 'Sauvageau, Guy',
//               person_id: 765,
//               text: 'Guy Sauvageau',
//               type_contenu: 'Person',
//               uid: 'in14491',
//             },
//           ],
//           emplacement: null,
//           id: 'I000018',
//           infrastructureId: 271,
//           infrastructureReception: null,
//           juridiction: {
//             acronym: 'IRIC',
//             acronyms: {
//               fr: 'IRIC',
//             },
//             createdAt: '2023-10-16T21:56:50-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               fr: null,
//             },
//             id: 'A000017',
//             lastUpdatedAt: '2023-10-16T17:59:58-0400',
//             names: {
//               fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//             },
//             organizationId: 10,
//             parent: {
//               acronym: null,
//               acronyms: {
//                 en: null,
//                 fr: null,
//               },
//               createdAt: '2023-10-16T21:48:30-0400',
//               dateBegin: null,
//               dateEnd: null,
//               descriptions: {
//                 en: null,
//                 fr: null,
//               },
//               id: 'G000001',
//               lastUpdatedAt: '2023-10-16T21:48:30-0400',
//               names: {
//                 en: 'University of Montreal',
//                 fr: 'Université de Montréal',
//               },
//               organizationId: 1,
//               parent: null,
//               pseudonymes: {
//                 en: null,
//                 fr: null,
//               },
//               text: 'Université de Montréal',
//               typeContenu: 'Etablissement',
//               typeEtablissement: {
//                 descriptions: {
//                   fr: null,
//                 },
//                 id: 1,
//                 nom: 'Académique',
//                 noms: {
//                   fr: 'Académique',
//                 },
//                 slug: 'academique',
//                 text: 'Académique',
//                 uid: 'academique',
//               },
//               uid: 'G000001',
//             },
//             pseudonymes: {
//               fr: null,
//             },
//             text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//             typeContenu: 'Unite',
//             uid: 'ur13772',
//           },
//           laboratoireInnovations: [],
//           lastUpdatedAt: '2023-10-17T06:35:35-0400',
//           locaux: [],
//           mettreEnVedette: false,
//           nom: "Plateforme de biologie in vivo de l'IRIC",
//           noms: {
//             fr: "Plateforme de biologie in vivo de l'IRIC",
//           },
//           projetFinancements: [],
//           pseudonyme:
//             'Institut de recherche en immunologie et en cancérologie - animalerie. IRIC animalerie',
//           responsablesSST: [],
//           responsablesTechnique: [
//             {
//               created_at: '2023-10-17T05:58:19-0400',
//               emails: [
//                 {
//                   address: '<EMAIL>',
//                   id: 7600,
//                   text: '<EMAIL>',
//                 },
//               ],
//               family_name: 'Julie',
//               given_name: 'Gervais',
//               id: '652e5abb9f702',
//               last_updated_at: null,
//               name_with_lastname_first: 'Julie, Gervais',
//               person_id: 9221,
//               text: 'Gervais Julie',
//               type_contenu: 'Person',
//               uid: null,
//             },
//           ],
//           statutInfrastructure: {
//             descriptions: {
//               fr: null,
//             },
//             id: 2,
//             nom: 'Actif',
//             noms: {
//               fr: 'Actif',
//             },
//             slug: 'actif',
//             text: 'Actif',
//             uid: 'actif',
//           },
//           telephone: null,
//           text: "Plateforme de biologie in vivo de l'IRIC",
//           totalEquipements: 54,
//           typeContenu: 'Infrastructure',
//           typeInfrastructure: {
//             descriptions: {
//               fr: null,
//             },
//             id: 2,
//             nom: 'Plateforme',
//             noms: {
//               fr: 'Plateforme',
//             },
//             slug: 'plateforme',
//             text: 'Plateforme',
//             uid: 'plateforme',
//           },
//           uid: 'ur14388',
//           unite: {
//             acronym: null,
//             acronyms: {
//               fr: null,
//             },
//             createdAt: '2023-10-16T17:59:15-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               fr: null,
//             },
//             id: '652db2331fa8b',
//             lastUpdatedAt: '2023-10-16T17:59:15-0400',
//             names: {
//               fr: 'Faculté de médecine - Département de médecine',
//             },
//             organizationId: 633,
//             parent: {
//               acronym: null,
//               acronyms: {
//                 en: null,
//                 fr: null,
//               },
//               createdAt: '2023-10-16T21:48:30-0400',
//               dateBegin: null,
//               dateEnd: null,
//               descriptions: {
//                 en: null,
//                 fr: null,
//               },
//               id: 'G000001',
//               lastUpdatedAt: '2023-10-16T21:48:30-0400',
//               names: {
//                 en: 'University of Montreal',
//                 fr: 'Université de Montréal',
//               },
//               organizationId: 1,
//               parent: null,
//               pseudonymes: {
//                 en: null,
//                 fr: null,
//               },
//               text: 'Université de Montréal',
//               typeContenu: 'Etablissement',
//               typeEtablissement: {
//                 descriptions: {
//                   fr: null,
//                 },
//                 id: 1,
//                 nom: 'Académique',
//                 noms: {
//                   fr: 'Académique',
//                 },
//                 slug: 'academique',
//                 text: 'Académique',
//                 uid: 'academique',
//               },
//               uid: 'G000001',
//             },
//             pseudonymes: {
//               fr: null,
//             },
//             text: 'Faculté de médecine - Département de médecine',
//             typeContenu: 'Unite',
//             uid: 'D203A0',
//           },
//           url: null,
//           visibilite: {
//             descriptions: {
//               fr: null,
//             },
//             id: 1,
//             nom: 'Public',
//             noms: {
//               fr: 'Public',
//             },
//             slug: 'public',
//             text: 'Public',
//             uid: 'public',
//           },
//         },
//         juridiction: {
//           acronym: 'IRIC',
//           acronyms: {
//             fr: 'IRIC',
//           },
//           createdAt: '2023-10-16T21:56:50-0400',
//           dateBegin: null,
//           dateEnd: null,
//           descriptions: {
//             fr: null,
//           },
//           id: 'A000017',
//           lastUpdatedAt: '2023-10-16T17:59:58-0400',
//           names: {
//             fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//           },
//           organizationId: 10,
//           parent: {
//             acronym: null,
//             acronyms: {
//               en: null,
//               fr: null,
//             },
//             createdAt: '2023-10-16T21:48:30-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               en: null,
//               fr: null,
//             },
//             id: 'G000001',
//             lastUpdatedAt: '2023-10-16T21:48:30-0400',
//             names: {
//               en: 'University of Montreal',
//               fr: 'Université de Montréal',
//             },
//             organizationId: 1,
//             parent: null,
//             pseudonymes: {
//               en: null,
//               fr: null,
//             },
//             text: 'Université de Montréal',
//             typeContenu: 'Etablissement',
//             typeEtablissement: {
//               descriptions: {
//                 fr: null,
//               },
//               id: 1,
//               nom: 'Académique',
//               noms: {
//                 fr: 'Académique',
//               },
//               slug: 'academique',
//               text: 'Académique',
//               uid: 'academique',
//             },
//             uid: 'G000001',
//           },
//           pseudonymes: {
//             fr: null,
//           },
//           text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//           typeContenu: 'Unite',
//           uid: 'ur13772',
//         },
//         lastUpdatedAt: null,
//         locaux: [],
//         maintenance: null,
//         maintenances: {
//           en: null,
//           fr: null,
//         },
//         manufacturier: null,
//         mettreEnVedette: false,
//         modele: null,
//         nom: 'Drying Tunnel (36 inches wide)',
//         noms: {
//           en: 'Drying Tunnel (36 inches wide)',
//           fr: 'Drying Tunnel (36 inches wide)',
//         },
//         numeroHomologation: null,
//         numeroInventaire: null,
//         numeroSerie: null,
//         photos: [],
//         poleExcellences: [],
//         precisions: [],
//         projetFinancements: [],
//         reparateurs: [],
//         responsablesSST: [],
//         responsablesTechnique: [],
//         risque: null,
//         risques: {
//           en: null,
//           fr: null,
//         },
//         secteurApplications: [],
//         statutDecommissionEquipement: {
//           id: 'date-decommission-indef',
//           noms: {
//             en: 'End of support undefined',
//             fr: 'Fin de support indéterminée',
//           },
//           uid: 'date-decommission-indef',
//         },
//         statutDureeVieEquipement: {
//           id: 'duree-vie-indef',
//           noms: {
//             en: 'Unknown Life expectancy',
//             fr: 'Espérance de vie indéterminée',
//           },
//           uid: 'duree-vie-indef',
//         },
//         techniques: [],
//         text: 'Drying Tunnel (36 inches wide)',
//         titulaire: {
//           created_at: '2023-10-16T18:02:58-0400',
//           emails: [
//             {
//               address: '<EMAIL>',
//               id: 678,
//               text: '<EMAIL>',
//             },
//           ],
//           family_name: 'Sauvageau',
//           given_name: 'Guy',
//           id: '652db312ee859',
//           last_updated_at: '2023-10-16T18:02:58-0400',
//           name_with_lastname_first: 'Sauvageau, Guy',
//           person_id: 765,
//           text: 'Guy Sauvageau',
//           type_contenu: 'Person',
//           uid: 'in14491',
//         },
//         typeContenu: 'Equipement',
//         typeEquipement: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Équipement',
//           noms: {
//             fr: 'Équipement',
//           },
//           slug: 'equipement',
//           text: 'Équipement',
//           uid: 'equipement',
//         },
//         utilisation: null,
//         utilisations: {
//           en: null,
//           fr: null,
//         },
//         videos: [],
//         visibilite: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Public',
//           noms: {
//             fr: 'Public',
//           },
//           slug: 'public',
//           text: 'Public',
//           uid: 'public',
//         },
//       },
//       {
//         _class: 'Rie\\RieCoreBundle\\Entity\\Equipement',
//         _className: 'Equipement',
//         _resourceName: 'equipement',
//         anneeFabrication: null,
//         batimentLocaux: [],
//         campusLocaux: [],
//         categories: [],
//         commentaire: null,
//         commentaires: {
//           en: null,
//           fr: null,
//         },
//         contratServices: [],
//         coutEspece: '190099.00',
//         coutNature: null,
//         coutTotal: 190099,
//         createdAt: '2023-10-17T06:11:32-0400',
//         dateAchat: null,
//         dateDecommission: null,
//         dateFin: null,
//         dateInstallation: null,
//         dateInstalOuAchat: null,
//         defaillance: null,
//         defaillances: {
//           en: null,
//           fr: null,
//         },
//         description: null,
//         descriptions: {
//           en: null,
//           fr: null,
//         },
//         detaillants: [],
//         disposition: null,
//         dispositions: {
//           en: null,
//           fr: null,
//         },
//         documents: [],
//         doi: null,
//         domaineRecherches: [],
//         emplacement: null,
//         equipementAccessoires: [],
//         equipementAutres: [],
//         equipementComposantes: [],
//         equipementDimension: null,
//         equipementDureeVie: null,
//         equipementEmplacement: null,
//         equipementId: 51,
//         equipementLogiciels: [],
//         equipementParents: [],
//         estEssais: false,
//         estMasquer: false,
//         etatEquipement: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Fonctionnel',
//           noms: {
//             fr: 'Fonctionnel',
//           },
//           slug: 'fonctionnel',
//           text: 'Fonctionnel',
//           uid: 'fonctionnel',
//         },
//         financement: null,
//         financements: {
//           en: null,
//           fr: null,
//         },
//         fournisseur: null,
//         frequenceMaintenance: null,
//         id: 'E000000055',
//         ID: 'equipement.E000000055',
//         infrastructure: {
//           acronyme: 'IRIC Biologie in vivo',
//           acronymes: {
//             fr: 'IRIC Biologie in vivo',
//           },
//           batimentLocaux: [],
//           campusLocaux: [],
//           createdAt: '2023-10-17T05:54:30-0400',
//           descriptions: {
//             fr: 'L’institut héberge une des plus grandes animaleries au Canada offrant un secteur exempt de pathogènes, un secteur conventionnel et une quarantaine autonome. En plus de l’hébergement et des supports technique et vétérinaire pour la recherche et la santé animale, l’animalerie de l’institut collabore avec la plateforme de biopharmacie de la Faculté de pharmacie de l’Université de Montréal pour offrir des services de pharmacocinétique, bioanalyse et toxicologie (www.pfbio.ca) .',
//           },
//           directeursTechnique: [
//             {
//               created_at: '2023-10-16T18:02:58-0400',
//               emails: [
//                 {
//                   address: '<EMAIL>',
//                   id: 678,
//                   text: '<EMAIL>',
//                 },
//               ],
//               family_name: 'Sauvageau',
//               given_name: 'Guy',
//               id: '652db312ee859',
//               last_updated_at: '2023-10-16T18:02:58-0400',
//               name_with_lastname_first: 'Sauvageau, Guy',
//               person_id: 765,
//               text: 'Guy Sauvageau',
//               type_contenu: 'Person',
//               uid: 'in14491',
//             },
//           ],
//           emplacement: null,
//           id: 'I000018',
//           infrastructureId: 271,
//           infrastructureReception: null,
//           juridiction: {
//             acronym: 'IRIC',
//             acronyms: {
//               fr: 'IRIC',
//             },
//             createdAt: '2023-10-16T21:56:50-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               fr: null,
//             },
//             id: 'A000017',
//             lastUpdatedAt: '2023-10-16T17:59:58-0400',
//             names: {
//               fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//             },
//             organizationId: 10,
//             parent: {
//               acronym: null,
//               acronyms: {
//                 en: null,
//                 fr: null,
//               },
//               createdAt: '2023-10-16T21:48:30-0400',
//               dateBegin: null,
//               dateEnd: null,
//               descriptions: {
//                 en: null,
//                 fr: null,
//               },
//               id: 'G000001',
//               lastUpdatedAt: '2023-10-16T21:48:30-0400',
//               names: {
//                 en: 'University of Montreal',
//                 fr: 'Université de Montréal',
//               },
//               organizationId: 1,
//               parent: null,
//               pseudonymes: {
//                 en: null,
//                 fr: null,
//               },
//               text: 'Université de Montréal',
//               typeContenu: 'Etablissement',
//               typeEtablissement: {
//                 descriptions: {
//                   fr: null,
//                 },
//                 id: 1,
//                 nom: 'Académique',
//                 noms: {
//                   fr: 'Académique',
//                 },
//                 slug: 'academique',
//                 text: 'Académique',
//                 uid: 'academique',
//               },
//               uid: 'G000001',
//             },
//             pseudonymes: {
//               fr: null,
//             },
//             text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//             typeContenu: 'Unite',
//             uid: 'ur13772',
//           },
//           laboratoireInnovations: [],
//           lastUpdatedAt: '2023-10-17T06:35:35-0400',
//           locaux: [],
//           mettreEnVedette: false,
//           nom: "Plateforme de biologie in vivo de l'IRIC",
//           noms: {
//             fr: "Plateforme de biologie in vivo de l'IRIC",
//           },
//           projetFinancements: [],
//           pseudonyme:
//             'Institut de recherche en immunologie et en cancérologie - animalerie. IRIC animalerie',
//           responsablesSST: [],
//           responsablesTechnique: [
//             {
//               created_at: '2023-10-17T05:58:19-0400',
//               emails: [
//                 {
//                   address: '<EMAIL>',
//                   id: 7600,
//                   text: '<EMAIL>',
//                 },
//               ],
//               family_name: 'Julie',
//               given_name: 'Gervais',
//               id: '652e5abb9f702',
//               last_updated_at: null,
//               name_with_lastname_first: 'Julie, Gervais',
//               person_id: 9221,
//               text: 'Gervais Julie',
//               type_contenu: 'Person',
//               uid: null,
//             },
//           ],
//           statutInfrastructure: {
//             descriptions: {
//               fr: null,
//             },
//             id: 2,
//             nom: 'Actif',
//             noms: {
//               fr: 'Actif',
//             },
//             slug: 'actif',
//             text: 'Actif',
//             uid: 'actif',
//           },
//           telephone: null,
//           text: "Plateforme de biologie in vivo de l'IRIC",
//           totalEquipements: 54,
//           typeContenu: 'Infrastructure',
//           typeInfrastructure: {
//             descriptions: {
//               fr: null,
//             },
//             id: 2,
//             nom: 'Plateforme',
//             noms: {
//               fr: 'Plateforme',
//             },
//             slug: 'plateforme',
//             text: 'Plateforme',
//             uid: 'plateforme',
//           },
//           uid: 'ur14388',
//           unite: {
//             acronym: null,
//             acronyms: {
//               fr: null,
//             },
//             createdAt: '2023-10-16T17:59:15-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               fr: null,
//             },
//             id: '652db2331fa8b',
//             lastUpdatedAt: '2023-10-16T17:59:15-0400',
//             names: {
//               fr: 'Faculté de médecine - Département de médecine',
//             },
//             organizationId: 633,
//             parent: {
//               acronym: null,
//               acronyms: {
//                 en: null,
//                 fr: null,
//               },
//               createdAt: '2023-10-16T21:48:30-0400',
//               dateBegin: null,
//               dateEnd: null,
//               descriptions: {
//                 en: null,
//                 fr: null,
//               },
//               id: 'G000001',
//               lastUpdatedAt: '2023-10-16T21:48:30-0400',
//               names: {
//                 en: 'University of Montreal',
//                 fr: 'Université de Montréal',
//               },
//               organizationId: 1,
//               parent: null,
//               pseudonymes: {
//                 en: null,
//                 fr: null,
//               },
//               text: 'Université de Montréal',
//               typeContenu: 'Etablissement',
//               typeEtablissement: {
//                 descriptions: {
//                   fr: null,
//                 },
//                 id: 1,
//                 nom: 'Académique',
//                 noms: {
//                   fr: 'Académique',
//                 },
//                 slug: 'academique',
//                 text: 'Académique',
//                 uid: 'academique',
//               },
//               uid: 'G000001',
//             },
//             pseudonymes: {
//               fr: null,
//             },
//             text: 'Faculté de médecine - Département de médecine',
//             typeContenu: 'Unite',
//             uid: 'D203A0',
//           },
//           url: null,
//           visibilite: {
//             descriptions: {
//               fr: null,
//             },
//             id: 1,
//             nom: 'Public',
//             noms: {
//               fr: 'Public',
//             },
//             slug: 'public',
//             text: 'Public',
//             uid: 'public',
//           },
//         },
//         juridiction: {
//           acronym: 'IRIC',
//           acronyms: {
//             fr: 'IRIC',
//           },
//           createdAt: '2023-10-16T21:56:50-0400',
//           dateBegin: null,
//           dateEnd: null,
//           descriptions: {
//             fr: null,
//           },
//           id: 'A000017',
//           lastUpdatedAt: '2023-10-16T17:59:58-0400',
//           names: {
//             fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//           },
//           organizationId: 10,
//           parent: {
//             acronym: null,
//             acronyms: {
//               en: null,
//               fr: null,
//             },
//             createdAt: '2023-10-16T21:48:30-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               en: null,
//               fr: null,
//             },
//             id: 'G000001',
//             lastUpdatedAt: '2023-10-16T21:48:30-0400',
//             names: {
//               en: 'University of Montreal',
//               fr: 'Université de Montréal',
//             },
//             organizationId: 1,
//             parent: null,
//             pseudonymes: {
//               en: null,
//               fr: null,
//             },
//             text: 'Université de Montréal',
//             typeContenu: 'Etablissement',
//             typeEtablissement: {
//               descriptions: {
//                 fr: null,
//               },
//               id: 1,
//               nom: 'Académique',
//               noms: {
//                 fr: 'Académique',
//               },
//               slug: 'academique',
//               text: 'Académique',
//               uid: 'academique',
//             },
//             uid: 'G000001',
//           },
//           pseudonymes: {
//             fr: null,
//           },
//           text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//           typeContenu: 'Unite',
//           uid: 'ur13772',
//         },
//         lastUpdatedAt: null,
//         locaux: [],
//         maintenance: null,
//         maintenances: {
//           en: null,
//           fr: null,
//         },
//         manufacturier: null,
//         mettreEnVedette: false,
//         modele: null,
//         nom: 'Cage washer Northstar R630',
//         noms: {
//           en: 'Cage washer Northstar R630',
//           fr: 'Cage washer Northstar R630',
//         },
//         numeroHomologation: null,
//         numeroInventaire: null,
//         numeroSerie: null,
//         photos: [],
//         poleExcellences: [],
//         precisions: [],
//         projetFinancements: [],
//         reparateurs: [],
//         responsablesSST: [],
//         responsablesTechnique: [],
//         risque: null,
//         risques: {
//           en: null,
//           fr: null,
//         },
//         secteurApplications: [],
//         statutDecommissionEquipement: {
//           id: 'date-decommission-indef',
//           noms: {
//             en: 'End of support undefined',
//             fr: 'Fin de support indéterminée',
//           },
//           uid: 'date-decommission-indef',
//         },
//         statutDureeVieEquipement: {
//           id: 'duree-vie-indef',
//           noms: {
//             en: 'Unknown Life expectancy',
//             fr: 'Espérance de vie indéterminée',
//           },
//           uid: 'duree-vie-indef',
//         },
//         techniques: [],
//         text: 'Cage washer Northstar R630',
//         titulaire: {
//           created_at: '2023-10-16T18:02:58-0400',
//           emails: [
//             {
//               address: '<EMAIL>',
//               id: 678,
//               text: '<EMAIL>',
//             },
//           ],
//           family_name: 'Sauvageau',
//           given_name: 'Guy',
//           id: '652db312ee859',
//           last_updated_at: '2023-10-16T18:02:58-0400',
//           name_with_lastname_first: 'Sauvageau, Guy',
//           person_id: 765,
//           text: 'Guy Sauvageau',
//           type_contenu: 'Person',
//           uid: 'in14491',
//         },
//         typeContenu: 'Equipement',
//         typeEquipement: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Équipement',
//           noms: {
//             fr: 'Équipement',
//           },
//           slug: 'equipement',
//           text: 'Équipement',
//           uid: 'equipement',
//         },
//         utilisation: null,
//         utilisations: {
//           en: null,
//           fr: null,
//         },
//         videos: [],
//         visibilite: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Public',
//           noms: {
//             fr: 'Public',
//           },
//           slug: 'public',
//           text: 'Public',
//           uid: 'public',
//         },
//       },
//       {
//         _class: 'Rie\\RieCoreBundle\\Entity\\Equipement',
//         _className: 'Equipement',
//         _resourceName: 'equipement',
//         anneeFabrication: null,
//         batimentLocaux: [],
//         campusLocaux: [],
//         categories: [],
//         commentaire: null,
//         commentaires: {
//           en: null,
//           fr: null,
//         },
//         contratServices: [],
//         coutEspece: '304178.00',
//         coutNature: null,
//         coutTotal: 304178,
//         createdAt: '2023-10-17T06:11:32-0400',
//         dateAchat: null,
//         dateDecommission: null,
//         dateFin: null,
//         dateInstallation: null,
//         dateInstalOuAchat: null,
//         defaillance: null,
//         defaillances: {
//           en: null,
//           fr: null,
//         },
//         description: null,
//         descriptions: {
//           en: null,
//           fr: null,
//         },
//         detaillants: [],
//         disposition: null,
//         dispositions: {
//           en: null,
//           fr: null,
//         },
//         documents: [],
//         doi: null,
//         domaineRecherches: [],
//         emplacement: null,
//         equipementAccessoires: [],
//         equipementAutres: [],
//         equipementComposantes: [],
//         equipementDimension: null,
//         equipementDureeVie: null,
//         equipementEmplacement: null,
//         equipementId: 52,
//         equipementLogiciels: [],
//         equipementParents: [],
//         estEssais: false,
//         estMasquer: false,
//         etatEquipement: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Fonctionnel',
//           noms: {
//             fr: 'Fonctionnel',
//           },
//           slug: 'fonctionnel',
//           text: 'Fonctionnel',
//           uid: 'fonctionnel',
//         },
//         financement: null,
//         financements: {
//           en: null,
//           fr: null,
//         },
//         fournisseur: null,
//         frequenceMaintenance: null,
//         id: 'E000000056',
//         ID: 'equipement.E000000056',
//         infrastructure: {
//           acronyme: 'IRIC Biologie in vivo',
//           acronymes: {
//             fr: 'IRIC Biologie in vivo',
//           },
//           batimentLocaux: [],
//           campusLocaux: [],
//           createdAt: '2023-10-17T05:54:30-0400',
//           descriptions: {
//             fr: 'L’institut héberge une des plus grandes animaleries au Canada offrant un secteur exempt de pathogènes, un secteur conventionnel et une quarantaine autonome. En plus de l’hébergement et des supports technique et vétérinaire pour la recherche et la santé animale, l’animalerie de l’institut collabore avec la plateforme de biopharmacie de la Faculté de pharmacie de l’Université de Montréal pour offrir des services de pharmacocinétique, bioanalyse et toxicologie (www.pfbio.ca) .',
//           },
//           directeursTechnique: [
//             {
//               created_at: '2023-10-16T18:02:58-0400',
//               emails: [
//                 {
//                   address: '<EMAIL>',
//                   id: 678,
//                   text: '<EMAIL>',
//                 },
//               ],
//               family_name: 'Sauvageau',
//               given_name: 'Guy',
//               id: '652db312ee859',
//               last_updated_at: '2023-10-16T18:02:58-0400',
//               name_with_lastname_first: 'Sauvageau, Guy',
//               person_id: 765,
//               text: 'Guy Sauvageau',
//               type_contenu: 'Person',
//               uid: 'in14491',
//             },
//           ],
//           emplacement: null,
//           id: 'I000018',
//           infrastructureId: 271,
//           infrastructureReception: null,
//           juridiction: {
//             acronym: 'IRIC',
//             acronyms: {
//               fr: 'IRIC',
//             },
//             createdAt: '2023-10-16T21:56:50-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               fr: null,
//             },
//             id: 'A000017',
//             lastUpdatedAt: '2023-10-16T17:59:58-0400',
//             names: {
//               fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//             },
//             organizationId: 10,
//             parent: {
//               acronym: null,
//               acronyms: {
//                 en: null,
//                 fr: null,
//               },
//               createdAt: '2023-10-16T21:48:30-0400',
//               dateBegin: null,
//               dateEnd: null,
//               descriptions: {
//                 en: null,
//                 fr: null,
//               },
//               id: 'G000001',
//               lastUpdatedAt: '2023-10-16T21:48:30-0400',
//               names: {
//                 en: 'University of Montreal',
//                 fr: 'Université de Montréal',
//               },
//               organizationId: 1,
//               parent: null,
//               pseudonymes: {
//                 en: null,
//                 fr: null,
//               },
//               text: 'Université de Montréal',
//               typeContenu: 'Etablissement',
//               typeEtablissement: {
//                 descriptions: {
//                   fr: null,
//                 },
//                 id: 1,
//                 nom: 'Académique',
//                 noms: {
//                   fr: 'Académique',
//                 },
//                 slug: 'academique',
//                 text: 'Académique',
//                 uid: 'academique',
//               },
//               uid: 'G000001',
//             },
//             pseudonymes: {
//               fr: null,
//             },
//             text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//             typeContenu: 'Unite',
//             uid: 'ur13772',
//           },
//           laboratoireInnovations: [],
//           lastUpdatedAt: '2023-10-17T06:35:35-0400',
//           locaux: [],
//           mettreEnVedette: false,
//           nom: "Plateforme de biologie in vivo de l'IRIC",
//           noms: {
//             fr: "Plateforme de biologie in vivo de l'IRIC",
//           },
//           projetFinancements: [],
//           pseudonyme:
//             'Institut de recherche en immunologie et en cancérologie - animalerie. IRIC animalerie',
//           responsablesSST: [],
//           responsablesTechnique: [
//             {
//               created_at: '2023-10-17T05:58:19-0400',
//               emails: [
//                 {
//                   address: '<EMAIL>',
//                   id: 7600,
//                   text: '<EMAIL>',
//                 },
//               ],
//               family_name: 'Julie',
//               given_name: 'Gervais',
//               id: '652e5abb9f702',
//               last_updated_at: null,
//               name_with_lastname_first: 'Julie, Gervais',
//               person_id: 9221,
//               text: 'Gervais Julie',
//               type_contenu: 'Person',
//               uid: null,
//             },
//           ],
//           statutInfrastructure: {
//             descriptions: {
//               fr: null,
//             },
//             id: 2,
//             nom: 'Actif',
//             noms: {
//               fr: 'Actif',
//             },
//             slug: 'actif',
//             text: 'Actif',
//             uid: 'actif',
//           },
//           telephone: null,
//           text: "Plateforme de biologie in vivo de l'IRIC",
//           totalEquipements: 54,
//           typeContenu: 'Infrastructure',
//           typeInfrastructure: {
//             descriptions: {
//               fr: null,
//             },
//             id: 2,
//             nom: 'Plateforme',
//             noms: {
//               fr: 'Plateforme',
//             },
//             slug: 'plateforme',
//             text: 'Plateforme',
//             uid: 'plateforme',
//           },
//           uid: 'ur14388',
//           unite: {
//             acronym: null,
//             acronyms: {
//               fr: null,
//             },
//             createdAt: '2023-10-16T17:59:15-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               fr: null,
//             },
//             id: '652db2331fa8b',
//             lastUpdatedAt: '2023-10-16T17:59:15-0400',
//             names: {
//               fr: 'Faculté de médecine - Département de médecine',
//             },
//             organizationId: 633,
//             parent: {
//               acronym: null,
//               acronyms: {
//                 en: null,
//                 fr: null,
//               },
//               createdAt: '2023-10-16T21:48:30-0400',
//               dateBegin: null,
//               dateEnd: null,
//               descriptions: {
//                 en: null,
//                 fr: null,
//               },
//               id: 'G000001',
//               lastUpdatedAt: '2023-10-16T21:48:30-0400',
//               names: {
//                 en: 'University of Montreal',
//                 fr: 'Université de Montréal',
//               },
//               organizationId: 1,
//               parent: null,
//               pseudonymes: {
//                 en: null,
//                 fr: null,
//               },
//               text: 'Université de Montréal',
//               typeContenu: 'Etablissement',
//               typeEtablissement: {
//                 descriptions: {
//                   fr: null,
//                 },
//                 id: 1,
//                 nom: 'Académique',
//                 noms: {
//                   fr: 'Académique',
//                 },
//                 slug: 'academique',
//                 text: 'Académique',
//                 uid: 'academique',
//               },
//               uid: 'G000001',
//             },
//             pseudonymes: {
//               fr: null,
//             },
//             text: 'Faculté de médecine - Département de médecine',
//             typeContenu: 'Unite',
//             uid: 'D203A0',
//           },
//           url: null,
//           visibilite: {
//             descriptions: {
//               fr: null,
//             },
//             id: 1,
//             nom: 'Public',
//             noms: {
//               fr: 'Public',
//             },
//             slug: 'public',
//             text: 'Public',
//             uid: 'public',
//           },
//         },
//         juridiction: {
//           acronym: 'IRIC',
//           acronyms: {
//             fr: 'IRIC',
//           },
//           createdAt: '2023-10-16T21:56:50-0400',
//           dateBegin: null,
//           dateEnd: null,
//           descriptions: {
//             fr: null,
//           },
//           id: 'A000017',
//           lastUpdatedAt: '2023-10-16T17:59:58-0400',
//           names: {
//             fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//           },
//           organizationId: 10,
//           parent: {
//             acronym: null,
//             acronyms: {
//               en: null,
//               fr: null,
//             },
//             createdAt: '2023-10-16T21:48:30-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               en: null,
//               fr: null,
//             },
//             id: 'G000001',
//             lastUpdatedAt: '2023-10-16T21:48:30-0400',
//             names: {
//               en: 'University of Montreal',
//               fr: 'Université de Montréal',
//             },
//             organizationId: 1,
//             parent: null,
//             pseudonymes: {
//               en: null,
//               fr: null,
//             },
//             text: 'Université de Montréal',
//             typeContenu: 'Etablissement',
//             typeEtablissement: {
//               descriptions: {
//                 fr: null,
//               },
//               id: 1,
//               nom: 'Académique',
//               noms: {
//                 fr: 'Académique',
//               },
//               slug: 'academique',
//               text: 'Académique',
//               uid: 'academique',
//             },
//             uid: 'G000001',
//           },
//           pseudonymes: {
//             fr: null,
//           },
//           text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//           typeContenu: 'Unite',
//           uid: 'ur13772',
//         },
//         lastUpdatedAt: null,
//         locaux: [],
//         maintenance: null,
//         maintenances: {
//           en: null,
//           fr: null,
//         },
//         manufacturier: null,
//         mettreEnVedette: false,
//         modele: null,
//         nom: 'Water purification system (RO for autoclave and  cage washer)',
//         noms: {
//           en: 'Water purification system (RO for autoclave and  cage washer)',
//           fr: 'Water purification system (RO for autoclave and  cage washer)',
//         },
//         numeroHomologation: null,
//         numeroInventaire: null,
//         numeroSerie: null,
//         photos: [],
//         poleExcellences: [],
//         precisions: [],
//         projetFinancements: [],
//         reparateurs: [],
//         responsablesSST: [],
//         responsablesTechnique: [],
//         risque: null,
//         risques: {
//           en: null,
//           fr: null,
//         },
//         secteurApplications: [],
//         statutDecommissionEquipement: {
//           id: 'date-decommission-indef',
//           noms: {
//             en: 'End of support undefined',
//             fr: 'Fin de support indéterminée',
//           },
//           uid: 'date-decommission-indef',
//         },
//         statutDureeVieEquipement: {
//           id: 'duree-vie-indef',
//           noms: {
//             en: 'Unknown Life expectancy',
//             fr: 'Espérance de vie indéterminée',
//           },
//           uid: 'duree-vie-indef',
//         },
//         techniques: [],
//         text: 'Water purification system (RO for autoclave and  cage washer)',
//         titulaire: {
//           created_at: '2023-10-16T18:02:58-0400',
//           emails: [
//             {
//               address: '<EMAIL>',
//               id: 678,
//               text: '<EMAIL>',
//             },
//           ],
//           family_name: 'Sauvageau',
//           given_name: 'Guy',
//           id: '652db312ee859',
//           last_updated_at: '2023-10-16T18:02:58-0400',
//           name_with_lastname_first: 'Sauvageau, Guy',
//           person_id: 765,
//           text: 'Guy Sauvageau',
//           type_contenu: 'Person',
//           uid: 'in14491',
//         },
//         typeContenu: 'Equipement',
//         typeEquipement: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Équipement',
//           noms: {
//             fr: 'Équipement',
//           },
//           slug: 'equipement',
//           text: 'Équipement',
//           uid: 'equipement',
//         },
//         utilisation: null,
//         utilisations: {
//           en: null,
//           fr: null,
//         },
//         videos: [],
//         visibilite: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Public',
//           noms: {
//             fr: 'Public',
//           },
//           slug: 'public',
//           text: 'Public',
//           uid: 'public',
//         },
//       },
//       {
//         _class: 'Rie\\RieCoreBundle\\Entity\\Equipement',
//         _className: 'Equipement',
//         _resourceName: 'equipement',
//         anneeFabrication: null,
//         batimentLocaux: [],
//         campusLocaux: [],
//         categories: [],
//         commentaire: null,
//         commentaires: {
//           en: null,
//           fr: null,
//         },
//         contratServices: [],
//         coutEspece: '361014.00',
//         coutNature: null,
//         coutTotal: 361014,
//         createdAt: '2023-10-17T06:11:32-0400',
//         dateAchat: null,
//         dateDecommission: null,
//         dateFin: null,
//         dateInstallation: null,
//         dateInstalOuAchat: null,
//         defaillance: null,
//         defaillances: {
//           en: null,
//           fr: null,
//         },
//         description: null,
//         descriptions: {
//           en: null,
//           fr: null,
//         },
//         detaillants: [],
//         disposition: null,
//         dispositions: {
//           en: null,
//           fr: null,
//         },
//         documents: [],
//         doi: null,
//         domaineRecherches: [],
//         emplacement: null,
//         equipementAccessoires: [],
//         equipementAutres: [],
//         equipementComposantes: [],
//         equipementDimension: null,
//         equipementDureeVie: null,
//         equipementEmplacement: null,
//         equipementId: 54,
//         equipementLogiciels: [],
//         equipementParents: [],
//         estEssais: false,
//         estMasquer: false,
//         etatEquipement: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Fonctionnel',
//           noms: {
//             fr: 'Fonctionnel',
//           },
//           slug: 'fonctionnel',
//           text: 'Fonctionnel',
//           uid: 'fonctionnel',
//         },
//         financement: null,
//         financements: {
//           en: null,
//           fr: null,
//         },
//         fournisseur: null,
//         frequenceMaintenance: null,
//         id: 'E000000058',
//         ID: 'equipement.E000000058',
//         infrastructure: {
//           acronyme: 'IRIC Biologie in vivo',
//           acronymes: {
//             fr: 'IRIC Biologie in vivo',
//           },
//           batimentLocaux: [],
//           campusLocaux: [],
//           createdAt: '2023-10-17T05:54:30-0400',
//           descriptions: {
//             fr: 'L’institut héberge une des plus grandes animaleries au Canada offrant un secteur exempt de pathogènes, un secteur conventionnel et une quarantaine autonome. En plus de l’hébergement et des supports technique et vétérinaire pour la recherche et la santé animale, l’animalerie de l’institut collabore avec la plateforme de biopharmacie de la Faculté de pharmacie de l’Université de Montréal pour offrir des services de pharmacocinétique, bioanalyse et toxicologie (www.pfbio.ca) .',
//           },
//           directeursTechnique: [
//             {
//               created_at: '2023-10-16T18:02:58-0400',
//               emails: [
//                 {
//                   address: '<EMAIL>',
//                   id: 678,
//                   text: '<EMAIL>',
//                 },
//               ],
//               family_name: 'Sauvageau',
//               given_name: 'Guy',
//               id: '652db312ee859',
//               last_updated_at: '2023-10-16T18:02:58-0400',
//               name_with_lastname_first: 'Sauvageau, Guy',
//               person_id: 765,
//               text: 'Guy Sauvageau',
//               type_contenu: 'Person',
//               uid: 'in14491',
//             },
//           ],
//           emplacement: null,
//           id: 'I000018',
//           infrastructureId: 271,
//           infrastructureReception: null,
//           juridiction: {
//             acronym: 'IRIC',
//             acronyms: {
//               fr: 'IRIC',
//             },
//             createdAt: '2023-10-16T21:56:50-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               fr: null,
//             },
//             id: 'A000017',
//             lastUpdatedAt: '2023-10-16T17:59:58-0400',
//             names: {
//               fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//             },
//             organizationId: 10,
//             parent: {
//               acronym: null,
//               acronyms: {
//                 en: null,
//                 fr: null,
//               },
//               createdAt: '2023-10-16T21:48:30-0400',
//               dateBegin: null,
//               dateEnd: null,
//               descriptions: {
//                 en: null,
//                 fr: null,
//               },
//               id: 'G000001',
//               lastUpdatedAt: '2023-10-16T21:48:30-0400',
//               names: {
//                 en: 'University of Montreal',
//                 fr: 'Université de Montréal',
//               },
//               organizationId: 1,
//               parent: null,
//               pseudonymes: {
//                 en: null,
//                 fr: null,
//               },
//               text: 'Université de Montréal',
//               typeContenu: 'Etablissement',
//               typeEtablissement: {
//                 descriptions: {
//                   fr: null,
//                 },
//                 id: 1,
//                 nom: 'Académique',
//                 noms: {
//                   fr: 'Académique',
//                 },
//                 slug: 'academique',
//                 text: 'Académique',
//                 uid: 'academique',
//               },
//               uid: 'G000001',
//             },
//             pseudonymes: {
//               fr: null,
//             },
//             text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//             typeContenu: 'Unite',
//             uid: 'ur13772',
//           },
//           laboratoireInnovations: [],
//           lastUpdatedAt: '2023-10-17T06:35:35-0400',
//           locaux: [],
//           mettreEnVedette: false,
//           nom: "Plateforme de biologie in vivo de l'IRIC",
//           noms: {
//             fr: "Plateforme de biologie in vivo de l'IRIC",
//           },
//           projetFinancements: [],
//           pseudonyme:
//             'Institut de recherche en immunologie et en cancérologie - animalerie. IRIC animalerie',
//           responsablesSST: [],
//           responsablesTechnique: [
//             {
//               created_at: '2023-10-17T05:58:19-0400',
//               emails: [
//                 {
//                   address: '<EMAIL>',
//                   id: 7600,
//                   text: '<EMAIL>',
//                 },
//               ],
//               family_name: 'Julie',
//               given_name: 'Gervais',
//               id: '652e5abb9f702',
//               last_updated_at: null,
//               name_with_lastname_first: 'Julie, Gervais',
//               person_id: 9221,
//               text: 'Gervais Julie',
//               type_contenu: 'Person',
//               uid: null,
//             },
//           ],
//           statutInfrastructure: {
//             descriptions: {
//               fr: null,
//             },
//             id: 2,
//             nom: 'Actif',
//             noms: {
//               fr: 'Actif',
//             },
//             slug: 'actif',
//             text: 'Actif',
//             uid: 'actif',
//           },
//           telephone: null,
//           text: "Plateforme de biologie in vivo de l'IRIC",
//           totalEquipements: 54,
//           typeContenu: 'Infrastructure',
//           typeInfrastructure: {
//             descriptions: {
//               fr: null,
//             },
//             id: 2,
//             nom: 'Plateforme',
//             noms: {
//               fr: 'Plateforme',
//             },
//             slug: 'plateforme',
//             text: 'Plateforme',
//             uid: 'plateforme',
//           },
//           uid: 'ur14388',
//           unite: {
//             acronym: null,
//             acronyms: {
//               fr: null,
//             },
//             createdAt: '2023-10-16T17:59:15-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               fr: null,
//             },
//             id: '652db2331fa8b',
//             lastUpdatedAt: '2023-10-16T17:59:15-0400',
//             names: {
//               fr: 'Faculté de médecine - Département de médecine',
//             },
//             organizationId: 633,
//             parent: {
//               acronym: null,
//               acronyms: {
//                 en: null,
//                 fr: null,
//               },
//               createdAt: '2023-10-16T21:48:30-0400',
//               dateBegin: null,
//               dateEnd: null,
//               descriptions: {
//                 en: null,
//                 fr: null,
//               },
//               id: 'G000001',
//               lastUpdatedAt: '2023-10-16T21:48:30-0400',
//               names: {
//                 en: 'University of Montreal',
//                 fr: 'Université de Montréal',
//               },
//               organizationId: 1,
//               parent: null,
//               pseudonymes: {
//                 en: null,
//                 fr: null,
//               },
//               text: 'Université de Montréal',
//               typeContenu: 'Etablissement',
//               typeEtablissement: {
//                 descriptions: {
//                   fr: null,
//                 },
//                 id: 1,
//                 nom: 'Académique',
//                 noms: {
//                   fr: 'Académique',
//                 },
//                 slug: 'academique',
//                 text: 'Académique',
//                 uid: 'academique',
//               },
//               uid: 'G000001',
//             },
//             pseudonymes: {
//               fr: null,
//             },
//             text: 'Faculté de médecine - Département de médecine',
//             typeContenu: 'Unite',
//             uid: 'D203A0',
//           },
//           url: null,
//           visibilite: {
//             descriptions: {
//               fr: null,
//             },
//             id: 1,
//             nom: 'Public',
//             noms: {
//               fr: 'Public',
//             },
//             slug: 'public',
//             text: 'Public',
//             uid: 'public',
//           },
//         },
//         juridiction: {
//           acronym: 'IRIC',
//           acronyms: {
//             fr: 'IRIC',
//           },
//           createdAt: '2023-10-16T21:56:50-0400',
//           dateBegin: null,
//           dateEnd: null,
//           descriptions: {
//             fr: null,
//           },
//           id: 'A000017',
//           lastUpdatedAt: '2023-10-16T17:59:58-0400',
//           names: {
//             fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//           },
//           organizationId: 10,
//           parent: {
//             acronym: null,
//             acronyms: {
//               en: null,
//               fr: null,
//             },
//             createdAt: '2023-10-16T21:48:30-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               en: null,
//               fr: null,
//             },
//             id: 'G000001',
//             lastUpdatedAt: '2023-10-16T21:48:30-0400',
//             names: {
//               en: 'University of Montreal',
//               fr: 'Université de Montréal',
//             },
//             organizationId: 1,
//             parent: null,
//             pseudonymes: {
//               en: null,
//               fr: null,
//             },
//             text: 'Université de Montréal',
//             typeContenu: 'Etablissement',
//             typeEtablissement: {
//               descriptions: {
//                 fr: null,
//               },
//               id: 1,
//               nom: 'Académique',
//               noms: {
//                 fr: 'Académique',
//               },
//               slug: 'academique',
//               text: 'Académique',
//               uid: 'academique',
//             },
//             uid: 'G000001',
//           },
//           pseudonymes: {
//             fr: null,
//           },
//           text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//           typeContenu: 'Unite',
//           uid: 'ur13772',
//         },
//         lastUpdatedAt: null,
//         locaux: [],
//         maintenance: null,
//         maintenances: {
//           en: null,
//           fr: null,
//         },
//         manufacturier: null,
//         mettreEnVedette: false,
//         modele: null,
//         nom: 'Autoclave, large (196pi3) for sterilization',
//         noms: {
//           en: 'Autoclave, large (196pi3) for sterilization',
//           fr: 'Autoclave, large (196pi3) for sterilization',
//         },
//         numeroHomologation: null,
//         numeroInventaire: null,
//         numeroSerie: null,
//         photos: [],
//         poleExcellences: [],
//         precisions: [],
//         projetFinancements: [],
//         reparateurs: [],
//         responsablesSST: [],
//         responsablesTechnique: [],
//         risque: null,
//         risques: {
//           en: null,
//           fr: null,
//         },
//         secteurApplications: [],
//         statutDecommissionEquipement: {
//           id: 'date-decommission-indef',
//           noms: {
//             en: 'End of support undefined',
//             fr: 'Fin de support indéterminée',
//           },
//           uid: 'date-decommission-indef',
//         },
//         statutDureeVieEquipement: {
//           id: 'duree-vie-indef',
//           noms: {
//             en: 'Unknown Life expectancy',
//             fr: 'Espérance de vie indéterminée',
//           },
//           uid: 'duree-vie-indef',
//         },
//         techniques: [],
//         text: 'Autoclave, large (196pi3) for sterilization',
//         titulaire: {
//           created_at: '2023-10-16T18:02:58-0400',
//           emails: [
//             {
//               address: '<EMAIL>',
//               id: 678,
//               text: '<EMAIL>',
//             },
//           ],
//           family_name: 'Sauvageau',
//           given_name: 'Guy',
//           id: '652db312ee859',
//           last_updated_at: '2023-10-16T18:02:58-0400',
//           name_with_lastname_first: 'Sauvageau, Guy',
//           person_id: 765,
//           text: 'Guy Sauvageau',
//           type_contenu: 'Person',
//           uid: 'in14491',
//         },
//         typeContenu: 'Equipement',
//         typeEquipement: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Équipement',
//           noms: {
//             fr: 'Équipement',
//           },
//           slug: 'equipement',
//           text: 'Équipement',
//           uid: 'equipement',
//         },
//         utilisation: null,
//         utilisations: {
//           en: null,
//           fr: null,
//         },
//         videos: [],
//         visibilite: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Public',
//           noms: {
//             fr: 'Public',
//           },
//           slug: 'public',
//           text: 'Public',
//           uid: 'public',
//         },
//       },
//       {
//         _class: 'Rie\\RieCoreBundle\\Entity\\Equipement',
//         _className: 'Equipement',
//         _resourceName: 'equipement',
//         anneeFabrication: null,
//         batimentLocaux: [],
//         campusLocaux: [],
//         categories: [],
//         commentaire: null,
//         commentaires: {
//           en: null,
//           fr: null,
//         },
//         contratServices: [],
//         coutEspece: '24324.00',
//         coutNature: null,
//         coutTotal: 24324,
//         createdAt: '2023-10-17T06:11:33-0400',
//         dateAchat: null,
//         dateDecommission: null,
//         dateFin: null,
//         dateInstallation: null,
//         dateInstalOuAchat: null,
//         defaillance: null,
//         defaillances: {
//           en: null,
//           fr: null,
//         },
//         description: null,
//         descriptions: {
//           en: null,
//           fr: null,
//         },
//         detaillants: [],
//         disposition: null,
//         dispositions: {
//           en: null,
//           fr: null,
//         },
//         documents: [],
//         doi: null,
//         domaineRecherches: [],
//         emplacement: null,
//         equipementAccessoires: [],
//         equipementAutres: [],
//         equipementComposantes: [],
//         equipementDimension: null,
//         equipementDureeVie: null,
//         equipementEmplacement: null,
//         equipementId: 58,
//         equipementLogiciels: [],
//         equipementParents: [],
//         estEssais: false,
//         estMasquer: false,
//         etatEquipement: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Fonctionnel',
//           noms: {
//             fr: 'Fonctionnel',
//           },
//           slug: 'fonctionnel',
//           text: 'Fonctionnel',
//           uid: 'fonctionnel',
//         },
//         financement: null,
//         financements: {
//           en: null,
//           fr: null,
//         },
//         fournisseur: null,
//         frequenceMaintenance: null,
//         id: 'E000000062',
//         ID: 'equipement.E000000062',
//         infrastructure: {
//           acronyme: 'IRIC Biologie in vivo',
//           acronymes: {
//             fr: 'IRIC Biologie in vivo',
//           },
//           batimentLocaux: [],
//           campusLocaux: [],
//           createdAt: '2023-10-17T05:54:30-0400',
//           descriptions: {
//             fr: 'L’institut héberge une des plus grandes animaleries au Canada offrant un secteur exempt de pathogènes, un secteur conventionnel et une quarantaine autonome. En plus de l’hébergement et des supports technique et vétérinaire pour la recherche et la santé animale, l’animalerie de l’institut collabore avec la plateforme de biopharmacie de la Faculté de pharmacie de l’Université de Montréal pour offrir des services de pharmacocinétique, bioanalyse et toxicologie (www.pfbio.ca) .',
//           },
//           directeursTechnique: [
//             {
//               created_at: '2023-10-16T18:02:58-0400',
//               emails: [
//                 {
//                   address: '<EMAIL>',
//                   id: 678,
//                   text: '<EMAIL>',
//                 },
//               ],
//               family_name: 'Sauvageau',
//               given_name: 'Guy',
//               id: '652db312ee859',
//               last_updated_at: '2023-10-16T18:02:58-0400',
//               name_with_lastname_first: 'Sauvageau, Guy',
//               person_id: 765,
//               text: 'Guy Sauvageau',
//               type_contenu: 'Person',
//               uid: 'in14491',
//             },
//           ],
//           emplacement: null,
//           id: 'I000018',
//           infrastructureId: 271,
//           infrastructureReception: null,
//           juridiction: {
//             acronym: 'IRIC',
//             acronyms: {
//               fr: 'IRIC',
//             },
//             createdAt: '2023-10-16T21:56:50-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               fr: null,
//             },
//             id: 'A000017',
//             lastUpdatedAt: '2023-10-16T17:59:58-0400',
//             names: {
//               fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//             },
//             organizationId: 10,
//             parent: {
//               acronym: null,
//               acronyms: {
//                 en: null,
//                 fr: null,
//               },
//               createdAt: '2023-10-16T21:48:30-0400',
//               dateBegin: null,
//               dateEnd: null,
//               descriptions: {
//                 en: null,
//                 fr: null,
//               },
//               id: 'G000001',
//               lastUpdatedAt: '2023-10-16T21:48:30-0400',
//               names: {
//                 en: 'University of Montreal',
//                 fr: 'Université de Montréal',
//               },
//               organizationId: 1,
//               parent: null,
//               pseudonymes: {
//                 en: null,
//                 fr: null,
//               },
//               text: 'Université de Montréal',
//               typeContenu: 'Etablissement',
//               typeEtablissement: {
//                 descriptions: {
//                   fr: null,
//                 },
//                 id: 1,
//                 nom: 'Académique',
//                 noms: {
//                   fr: 'Académique',
//                 },
//                 slug: 'academique',
//                 text: 'Académique',
//                 uid: 'academique',
//               },
//               uid: 'G000001',
//             },
//             pseudonymes: {
//               fr: null,
//             },
//             text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//             typeContenu: 'Unite',
//             uid: 'ur13772',
//           },
//           laboratoireInnovations: [],
//           lastUpdatedAt: '2023-10-17T06:35:35-0400',
//           locaux: [],
//           mettreEnVedette: false,
//           nom: "Plateforme de biologie in vivo de l'IRIC",
//           noms: {
//             fr: "Plateforme de biologie in vivo de l'IRIC",
//           },
//           projetFinancements: [],
//           pseudonyme:
//             'Institut de recherche en immunologie et en cancérologie - animalerie. IRIC animalerie',
//           responsablesSST: [],
//           responsablesTechnique: [
//             {
//               created_at: '2023-10-17T05:58:19-0400',
//               emails: [
//                 {
//                   address: '<EMAIL>',
//                   id: 7600,
//                   text: '<EMAIL>',
//                 },
//               ],
//               family_name: 'Julie',
//               given_name: 'Gervais',
//               id: '652e5abb9f702',
//               last_updated_at: null,
//               name_with_lastname_first: 'Julie, Gervais',
//               person_id: 9221,
//               text: 'Gervais Julie',
//               type_contenu: 'Person',
//               uid: null,
//             },
//           ],
//           statutInfrastructure: {
//             descriptions: {
//               fr: null,
//             },
//             id: 2,
//             nom: 'Actif',
//             noms: {
//               fr: 'Actif',
//             },
//             slug: 'actif',
//             text: 'Actif',
//             uid: 'actif',
//           },
//           telephone: null,
//           text: "Plateforme de biologie in vivo de l'IRIC",
//           totalEquipements: 54,
//           typeContenu: 'Infrastructure',
//           typeInfrastructure: {
//             descriptions: {
//               fr: null,
//             },
//             id: 2,
//             nom: 'Plateforme',
//             noms: {
//               fr: 'Plateforme',
//             },
//             slug: 'plateforme',
//             text: 'Plateforme',
//             uid: 'plateforme',
//           },
//           uid: 'ur14388',
//           unite: {
//             acronym: null,
//             acronyms: {
//               fr: null,
//             },
//             createdAt: '2023-10-16T17:59:15-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               fr: null,
//             },
//             id: '652db2331fa8b',
//             lastUpdatedAt: '2023-10-16T17:59:15-0400',
//             names: {
//               fr: 'Faculté de médecine - Département de médecine',
//             },
//             organizationId: 633,
//             parent: {
//               acronym: null,
//               acronyms: {
//                 en: null,
//                 fr: null,
//               },
//               createdAt: '2023-10-16T21:48:30-0400',
//               dateBegin: null,
//               dateEnd: null,
//               descriptions: {
//                 en: null,
//                 fr: null,
//               },
//               id: 'G000001',
//               lastUpdatedAt: '2023-10-16T21:48:30-0400',
//               names: {
//                 en: 'University of Montreal',
//                 fr: 'Université de Montréal',
//               },
//               organizationId: 1,
//               parent: null,
//               pseudonymes: {
//                 en: null,
//                 fr: null,
//               },
//               text: 'Université de Montréal',
//               typeContenu: 'Etablissement',
//               typeEtablissement: {
//                 descriptions: {
//                   fr: null,
//                 },
//                 id: 1,
//                 nom: 'Académique',
//                 noms: {
//                   fr: 'Académique',
//                 },
//                 slug: 'academique',
//                 text: 'Académique',
//                 uid: 'academique',
//               },
//               uid: 'G000001',
//             },
//             pseudonymes: {
//               fr: null,
//             },
//             text: 'Faculté de médecine - Département de médecine',
//             typeContenu: 'Unite',
//             uid: 'D203A0',
//           },
//           url: null,
//           visibilite: {
//             descriptions: {
//               fr: null,
//             },
//             id: 1,
//             nom: 'Public',
//             noms: {
//               fr: 'Public',
//             },
//             slug: 'public',
//             text: 'Public',
//             uid: 'public',
//           },
//         },
//         juridiction: {
//           acronym: 'IRIC',
//           acronyms: {
//             fr: 'IRIC',
//           },
//           createdAt: '2023-10-16T21:56:50-0400',
//           dateBegin: null,
//           dateEnd: null,
//           descriptions: {
//             fr: null,
//           },
//           id: 'A000017',
//           lastUpdatedAt: '2023-10-16T17:59:58-0400',
//           names: {
//             fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//           },
//           organizationId: 10,
//           parent: {
//             acronym: null,
//             acronyms: {
//               en: null,
//               fr: null,
//             },
//             createdAt: '2023-10-16T21:48:30-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               en: null,
//               fr: null,
//             },
//             id: 'G000001',
//             lastUpdatedAt: '2023-10-16T21:48:30-0400',
//             names: {
//               en: 'University of Montreal',
//               fr: 'Université de Montréal',
//             },
//             organizationId: 1,
//             parent: null,
//             pseudonymes: {
//               en: null,
//               fr: null,
//             },
//             text: 'Université de Montréal',
//             typeContenu: 'Etablissement',
//             typeEtablissement: {
//               descriptions: {
//                 fr: null,
//               },
//               id: 1,
//               nom: 'Académique',
//               noms: {
//                 fr: 'Académique',
//               },
//               slug: 'academique',
//               text: 'Académique',
//               uid: 'academique',
//             },
//             uid: 'G000001',
//           },
//           pseudonymes: {
//             fr: null,
//           },
//           text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//           typeContenu: 'Unite',
//           uid: 'ur13772',
//         },
//         lastUpdatedAt: null,
//         locaux: [],
//         maintenance: null,
//         maintenances: {
//           en: null,
//           fr: null,
//         },
//         manufacturier: null,
//         mettreEnVedette: false,
//         modele: null,
//         nom: "Douche d'air Biobubble",
//         noms: {
//           en: 'Air shower (Biobubble)',
//           fr: "Douche d'air Biobubble",
//         },
//         numeroHomologation: null,
//         numeroInventaire: null,
//         numeroSerie: null,
//         photos: [],
//         poleExcellences: [],
//         precisions: [],
//         projetFinancements: [],
//         reparateurs: [],
//         responsablesSST: [],
//         responsablesTechnique: [],
//         risque: null,
//         risques: {
//           en: null,
//           fr: null,
//         },
//         secteurApplications: [],
//         statutDecommissionEquipement: {
//           id: 'date-decommission-indef',
//           noms: {
//             en: 'End of support undefined',
//             fr: 'Fin de support indéterminée',
//           },
//           uid: 'date-decommission-indef',
//         },
//         statutDureeVieEquipement: {
//           id: 'duree-vie-indef',
//           noms: {
//             en: 'Unknown Life expectancy',
//             fr: 'Espérance de vie indéterminée',
//           },
//           uid: 'duree-vie-indef',
//         },
//         techniques: [],
//         text: "Douche d'air Biobubble",
//         titulaire: {
//           created_at: '2023-10-16T18:02:58-0400',
//           emails: [
//             {
//               address: '<EMAIL>',
//               id: 678,
//               text: '<EMAIL>',
//             },
//           ],
//           family_name: 'Sauvageau',
//           given_name: 'Guy',
//           id: '652db312ee859',
//           last_updated_at: '2023-10-16T18:02:58-0400',
//           name_with_lastname_first: 'Sauvageau, Guy',
//           person_id: 765,
//           text: 'Guy Sauvageau',
//           type_contenu: 'Person',
//           uid: 'in14491',
//         },
//         typeContenu: 'Equipement',
//         typeEquipement: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Équipement',
//           noms: {
//             fr: 'Équipement',
//           },
//           slug: 'equipement',
//           text: 'Équipement',
//           uid: 'equipement',
//         },
//         utilisation: null,
//         utilisations: {
//           en: null,
//           fr: null,
//         },
//         videos: [],
//         visibilite: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Public',
//           noms: {
//             fr: 'Public',
//           },
//           slug: 'public',
//           text: 'Public',
//           uid: 'public',
//         },
//       },
//       {
//         _class: 'Rie\\RieCoreBundle\\Entity\\Equipement',
//         _className: 'Equipement',
//         _resourceName: 'equipement',
//         anneeFabrication: null,
//         batimentLocaux: [],
//         campusLocaux: [],
//         categories: [],
//         commentaire: null,
//         commentaires: {
//           en: null,
//           fr: null,
//         },
//         contratServices: [],
//         coutEspece: '28235.00',
//         coutNature: null,
//         coutTotal: 28235,
//         createdAt: '2023-10-17T06:11:33-0400',
//         dateAchat: null,
//         dateDecommission: null,
//         dateFin: null,
//         dateInstallation: null,
//         dateInstalOuAchat: null,
//         defaillance: null,
//         defaillances: {
//           en: null,
//           fr: null,
//         },
//         description: null,
//         descriptions: {
//           en: null,
//           fr: null,
//         },
//         detaillants: [],
//         disposition: null,
//         dispositions: {
//           en: null,
//           fr: null,
//         },
//         documents: [],
//         doi: null,
//         domaineRecherches: [],
//         emplacement: null,
//         equipementAccessoires: [],
//         equipementAutres: [],
//         equipementComposantes: [],
//         equipementDimension: null,
//         equipementDureeVie: null,
//         equipementEmplacement: null,
//         equipementId: 59,
//         equipementLogiciels: [],
//         equipementParents: [],
//         estEssais: false,
//         estMasquer: false,
//         etatEquipement: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Fonctionnel',
//           noms: {
//             fr: 'Fonctionnel',
//           },
//           slug: 'fonctionnel',
//           text: 'Fonctionnel',
//           uid: 'fonctionnel',
//         },
//         financement: null,
//         financements: {
//           en: null,
//           fr: null,
//         },
//         fournisseur: null,
//         frequenceMaintenance: null,
//         id: 'E000000063',
//         ID: 'equipement.E000000063',
//         infrastructure: {
//           acronyme: 'IRIC Biologie in vivo',
//           acronymes: {
//             fr: 'IRIC Biologie in vivo',
//           },
//           batimentLocaux: [],
//           campusLocaux: [],
//           createdAt: '2023-10-17T05:54:30-0400',
//           descriptions: {
//             fr: 'L’institut héberge une des plus grandes animaleries au Canada offrant un secteur exempt de pathogènes, un secteur conventionnel et une quarantaine autonome. En plus de l’hébergement et des supports technique et vétérinaire pour la recherche et la santé animale, l’animalerie de l’institut collabore avec la plateforme de biopharmacie de la Faculté de pharmacie de l’Université de Montréal pour offrir des services de pharmacocinétique, bioanalyse et toxicologie (www.pfbio.ca) .',
//           },
//           directeursTechnique: [
//             {
//               created_at: '2023-10-16T18:02:58-0400',
//               emails: [
//                 {
//                   address: '<EMAIL>',
//                   id: 678,
//                   text: '<EMAIL>',
//                 },
//               ],
//               family_name: 'Sauvageau',
//               given_name: 'Guy',
//               id: '652db312ee859',
//               last_updated_at: '2023-10-16T18:02:58-0400',
//               name_with_lastname_first: 'Sauvageau, Guy',
//               person_id: 765,
//               text: 'Guy Sauvageau',
//               type_contenu: 'Person',
//               uid: 'in14491',
//             },
//           ],
//           emplacement: null,
//           id: 'I000018',
//           infrastructureId: 271,
//           infrastructureReception: null,
//           juridiction: {
//             acronym: 'IRIC',
//             acronyms: {
//               fr: 'IRIC',
//             },
//             createdAt: '2023-10-16T21:56:50-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               fr: null,
//             },
//             id: 'A000017',
//             lastUpdatedAt: '2023-10-16T17:59:58-0400',
//             names: {
//               fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//             },
//             organizationId: 10,
//             parent: {
//               acronym: null,
//               acronyms: {
//                 en: null,
//                 fr: null,
//               },
//               createdAt: '2023-10-16T21:48:30-0400',
//               dateBegin: null,
//               dateEnd: null,
//               descriptions: {
//                 en: null,
//                 fr: null,
//               },
//               id: 'G000001',
//               lastUpdatedAt: '2023-10-16T21:48:30-0400',
//               names: {
//                 en: 'University of Montreal',
//                 fr: 'Université de Montréal',
//               },
//               organizationId: 1,
//               parent: null,
//               pseudonymes: {
//                 en: null,
//                 fr: null,
//               },
//               text: 'Université de Montréal',
//               typeContenu: 'Etablissement',
//               typeEtablissement: {
//                 descriptions: {
//                   fr: null,
//                 },
//                 id: 1,
//                 nom: 'Académique',
//                 noms: {
//                   fr: 'Académique',
//                 },
//                 slug: 'academique',
//                 text: 'Académique',
//                 uid: 'academique',
//               },
//               uid: 'G000001',
//             },
//             pseudonymes: {
//               fr: null,
//             },
//             text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//             typeContenu: 'Unite',
//             uid: 'ur13772',
//           },
//           laboratoireInnovations: [],
//           lastUpdatedAt: '2023-10-17T06:35:35-0400',
//           locaux: [],
//           mettreEnVedette: false,
//           nom: "Plateforme de biologie in vivo de l'IRIC",
//           noms: {
//             fr: "Plateforme de biologie in vivo de l'IRIC",
//           },
//           projetFinancements: [],
//           pseudonyme:
//             'Institut de recherche en immunologie et en cancérologie - animalerie. IRIC animalerie',
//           responsablesSST: [],
//           responsablesTechnique: [
//             {
//               created_at: '2023-10-17T05:58:19-0400',
//               emails: [
//                 {
//                   address: '<EMAIL>',
//                   id: 7600,
//                   text: '<EMAIL>',
//                 },
//               ],
//               family_name: 'Julie',
//               given_name: 'Gervais',
//               id: '652e5abb9f702',
//               last_updated_at: null,
//               name_with_lastname_first: 'Julie, Gervais',
//               person_id: 9221,
//               text: 'Gervais Julie',
//               type_contenu: 'Person',
//               uid: null,
//             },
//           ],
//           statutInfrastructure: {
//             descriptions: {
//               fr: null,
//             },
//             id: 2,
//             nom: 'Actif',
//             noms: {
//               fr: 'Actif',
//             },
//             slug: 'actif',
//             text: 'Actif',
//             uid: 'actif',
//           },
//           telephone: null,
//           text: "Plateforme de biologie in vivo de l'IRIC",
//           totalEquipements: 54,
//           typeContenu: 'Infrastructure',
//           typeInfrastructure: {
//             descriptions: {
//               fr: null,
//             },
//             id: 2,
//             nom: 'Plateforme',
//             noms: {
//               fr: 'Plateforme',
//             },
//             slug: 'plateforme',
//             text: 'Plateforme',
//             uid: 'plateforme',
//           },
//           uid: 'ur14388',
//           unite: {
//             acronym: null,
//             acronyms: {
//               fr: null,
//             },
//             createdAt: '2023-10-16T17:59:15-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               fr: null,
//             },
//             id: '652db2331fa8b',
//             lastUpdatedAt: '2023-10-16T17:59:15-0400',
//             names: {
//               fr: 'Faculté de médecine - Département de médecine',
//             },
//             organizationId: 633,
//             parent: {
//               acronym: null,
//               acronyms: {
//                 en: null,
//                 fr: null,
//               },
//               createdAt: '2023-10-16T21:48:30-0400',
//               dateBegin: null,
//               dateEnd: null,
//               descriptions: {
//                 en: null,
//                 fr: null,
//               },
//               id: 'G000001',
//               lastUpdatedAt: '2023-10-16T21:48:30-0400',
//               names: {
//                 en: 'University of Montreal',
//                 fr: 'Université de Montréal',
//               },
//               organizationId: 1,
//               parent: null,
//               pseudonymes: {
//                 en: null,
//                 fr: null,
//               },
//               text: 'Université de Montréal',
//               typeContenu: 'Etablissement',
//               typeEtablissement: {
//                 descriptions: {
//                   fr: null,
//                 },
//                 id: 1,
//                 nom: 'Académique',
//                 noms: {
//                   fr: 'Académique',
//                 },
//                 slug: 'academique',
//                 text: 'Académique',
//                 uid: 'academique',
//               },
//               uid: 'G000001',
//             },
//             pseudonymes: {
//               fr: null,
//             },
//             text: 'Faculté de médecine - Département de médecine',
//             typeContenu: 'Unite',
//             uid: 'D203A0',
//           },
//           url: null,
//           visibilite: {
//             descriptions: {
//               fr: null,
//             },
//             id: 1,
//             nom: 'Public',
//             noms: {
//               fr: 'Public',
//             },
//             slug: 'public',
//             text: 'Public',
//             uid: 'public',
//           },
//         },
//         juridiction: {
//           acronym: 'IRIC',
//           acronyms: {
//             fr: 'IRIC',
//           },
//           createdAt: '2023-10-16T21:56:50-0400',
//           dateBegin: null,
//           dateEnd: null,
//           descriptions: {
//             fr: null,
//           },
//           id: 'A000017',
//           lastUpdatedAt: '2023-10-16T17:59:58-0400',
//           names: {
//             fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//           },
//           organizationId: 10,
//           parent: {
//             acronym: null,
//             acronyms: {
//               en: null,
//               fr: null,
//             },
//             createdAt: '2023-10-16T21:48:30-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               en: null,
//               fr: null,
//             },
//             id: 'G000001',
//             lastUpdatedAt: '2023-10-16T21:48:30-0400',
//             names: {
//               en: 'University of Montreal',
//               fr: 'Université de Montréal',
//             },
//             organizationId: 1,
//             parent: null,
//             pseudonymes: {
//               en: null,
//               fr: null,
//             },
//             text: 'Université de Montréal',
//             typeContenu: 'Etablissement',
//             typeEtablissement: {
//               descriptions: {
//                 fr: null,
//               },
//               id: 1,
//               nom: 'Académique',
//               noms: {
//                 fr: 'Académique',
//               },
//               slug: 'academique',
//               text: 'Académique',
//               uid: 'academique',
//             },
//             uid: 'G000001',
//           },
//           pseudonymes: {
//             fr: null,
//           },
//           text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//           typeContenu: 'Unite',
//           uid: 'ur13772',
//         },
//         lastUpdatedAt: null,
//         locaux: [],
//         maintenance: null,
//         maintenances: {
//           en: null,
//           fr: null,
//         },
//         manufacturier: null,
//         mettreEnVedette: false,
//         modele: null,
//         nom: 'Isolette',
//         noms: {
//           en: '',
//           fr: 'Isolette',
//         },
//         numeroHomologation: null,
//         numeroInventaire: null,
//         numeroSerie: null,
//         photos: [],
//         poleExcellences: [],
//         precisions: [],
//         projetFinancements: [],
//         reparateurs: [],
//         responsablesSST: [],
//         responsablesTechnique: [],
//         risque: null,
//         risques: {
//           en: null,
//           fr: null,
//         },
//         secteurApplications: [],
//         statutDecommissionEquipement: {
//           id: 'date-decommission-indef',
//           noms: {
//             en: 'End of support undefined',
//             fr: 'Fin de support indéterminée',
//           },
//           uid: 'date-decommission-indef',
//         },
//         statutDureeVieEquipement: {
//           id: 'duree-vie-indef',
//           noms: {
//             en: 'Unknown Life expectancy',
//             fr: 'Espérance de vie indéterminée',
//           },
//           uid: 'duree-vie-indef',
//         },
//         techniques: [],
//         text: 'Isolette',
//         titulaire: {
//           created_at: '2023-10-16T18:02:58-0400',
//           emails: [
//             {
//               address: '<EMAIL>',
//               id: 678,
//               text: '<EMAIL>',
//             },
//           ],
//           family_name: 'Sauvageau',
//           given_name: 'Guy',
//           id: '652db312ee859',
//           last_updated_at: '2023-10-16T18:02:58-0400',
//           name_with_lastname_first: 'Sauvageau, Guy',
//           person_id: 765,
//           text: 'Guy Sauvageau',
//           type_contenu: 'Person',
//           uid: 'in14491',
//         },
//         typeContenu: 'Equipement',
//         typeEquipement: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Équipement',
//           noms: {
//             fr: 'Équipement',
//           },
//           slug: 'equipement',
//           text: 'Équipement',
//           uid: 'equipement',
//         },
//         utilisation: null,
//         utilisations: {
//           en: null,
//           fr: null,
//         },
//         videos: [],
//         visibilite: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Public',
//           noms: {
//             fr: 'Public',
//           },
//           slug: 'public',
//           text: 'Public',
//           uid: 'public',
//         },
//       },
//       {
//         _class: 'Rie\\RieCoreBundle\\Entity\\Equipement',
//         _className: 'Equipement',
//         _resourceName: 'equipement',
//         anneeFabrication: null,
//         batimentLocaux: [],
//         campusLocaux: [],
//         categories: [],
//         commentaire: null,
//         commentaires: {
//           en: null,
//           fr: null,
//         },
//         contratServices: [],
//         coutEspece: '10453.00',
//         coutNature: null,
//         coutTotal: 10453,
//         createdAt: '2023-10-17T06:11:33-0400',
//         dateAchat: null,
//         dateDecommission: null,
//         dateFin: null,
//         dateInstallation: null,
//         dateInstalOuAchat: null,
//         defaillance: null,
//         defaillances: {
//           en: null,
//           fr: null,
//         },
//         description: null,
//         descriptions: {
//           en: null,
//           fr: null,
//         },
//         detaillants: [],
//         disposition: null,
//         dispositions: {
//           en: null,
//           fr: null,
//         },
//         documents: [],
//         doi: null,
//         domaineRecherches: [],
//         emplacement: null,
//         equipementAccessoires: [],
//         equipementAutres: [],
//         equipementComposantes: [],
//         equipementDimension: null,
//         equipementDureeVie: null,
//         equipementEmplacement: null,
//         equipementId: 60,
//         equipementLogiciels: [],
//         equipementParents: [],
//         estEssais: false,
//         estMasquer: false,
//         etatEquipement: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Fonctionnel',
//           noms: {
//             fr: 'Fonctionnel',
//           },
//           slug: 'fonctionnel',
//           text: 'Fonctionnel',
//           uid: 'fonctionnel',
//         },
//         financement: null,
//         financements: {
//           en: null,
//           fr: null,
//         },
//         fournisseur: null,
//         frequenceMaintenance: null,
//         id: 'E000000064',
//         ID: 'equipement.E000000064',
//         infrastructure: {
//           acronyme: 'IRIC Biologie in vivo',
//           acronymes: {
//             fr: 'IRIC Biologie in vivo',
//           },
//           batimentLocaux: [],
//           campusLocaux: [],
//           createdAt: '2023-10-17T05:54:30-0400',
//           descriptions: {
//             fr: 'L’institut héberge une des plus grandes animaleries au Canada offrant un secteur exempt de pathogènes, un secteur conventionnel et une quarantaine autonome. En plus de l’hébergement et des supports technique et vétérinaire pour la recherche et la santé animale, l’animalerie de l’institut collabore avec la plateforme de biopharmacie de la Faculté de pharmacie de l’Université de Montréal pour offrir des services de pharmacocinétique, bioanalyse et toxicologie (www.pfbio.ca) .',
//           },
//           directeursTechnique: [
//             {
//               created_at: '2023-10-16T18:02:58-0400',
//               emails: [
//                 {
//                   address: '<EMAIL>',
//                   id: 678,
//                   text: '<EMAIL>',
//                 },
//               ],
//               family_name: 'Sauvageau',
//               given_name: 'Guy',
//               id: '652db312ee859',
//               last_updated_at: '2023-10-16T18:02:58-0400',
//               name_with_lastname_first: 'Sauvageau, Guy',
//               person_id: 765,
//               text: 'Guy Sauvageau',
//               type_contenu: 'Person',
//               uid: 'in14491',
//             },
//           ],
//           emplacement: null,
//           id: 'I000018',
//           infrastructureId: 271,
//           infrastructureReception: null,
//           juridiction: {
//             acronym: 'IRIC',
//             acronyms: {
//               fr: 'IRIC',
//             },
//             createdAt: '2023-10-16T21:56:50-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               fr: null,
//             },
//             id: 'A000017',
//             lastUpdatedAt: '2023-10-16T17:59:58-0400',
//             names: {
//               fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//             },
//             organizationId: 10,
//             parent: {
//               acronym: null,
//               acronyms: {
//                 en: null,
//                 fr: null,
//               },
//               createdAt: '2023-10-16T21:48:30-0400',
//               dateBegin: null,
//               dateEnd: null,
//               descriptions: {
//                 en: null,
//                 fr: null,
//               },
//               id: 'G000001',
//               lastUpdatedAt: '2023-10-16T21:48:30-0400',
//               names: {
//                 en: 'University of Montreal',
//                 fr: 'Université de Montréal',
//               },
//               organizationId: 1,
//               parent: null,
//               pseudonymes: {
//                 en: null,
//                 fr: null,
//               },
//               text: 'Université de Montréal',
//               typeContenu: 'Etablissement',
//               typeEtablissement: {
//                 descriptions: {
//                   fr: null,
//                 },
//                 id: 1,
//                 nom: 'Académique',
//                 noms: {
//                   fr: 'Académique',
//                 },
//                 slug: 'academique',
//                 text: 'Académique',
//                 uid: 'academique',
//               },
//               uid: 'G000001',
//             },
//             pseudonymes: {
//               fr: null,
//             },
//             text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//             typeContenu: 'Unite',
//             uid: 'ur13772',
//           },
//           laboratoireInnovations: [],
//           lastUpdatedAt: '2023-10-17T06:35:35-0400',
//           locaux: [],
//           mettreEnVedette: false,
//           nom: "Plateforme de biologie in vivo de l'IRIC",
//           noms: {
//             fr: "Plateforme de biologie in vivo de l'IRIC",
//           },
//           projetFinancements: [],
//           pseudonyme:
//             'Institut de recherche en immunologie et en cancérologie - animalerie. IRIC animalerie',
//           responsablesSST: [],
//           responsablesTechnique: [
//             {
//               created_at: '2023-10-17T05:58:19-0400',
//               emails: [
//                 {
//                   address: '<EMAIL>',
//                   id: 7600,
//                   text: '<EMAIL>',
//                 },
//               ],
//               family_name: 'Julie',
//               given_name: 'Gervais',
//               id: '652e5abb9f702',
//               last_updated_at: null,
//               name_with_lastname_first: 'Julie, Gervais',
//               person_id: 9221,
//               text: 'Gervais Julie',
//               type_contenu: 'Person',
//               uid: null,
//             },
//           ],
//           statutInfrastructure: {
//             descriptions: {
//               fr: null,
//             },
//             id: 2,
//             nom: 'Actif',
//             noms: {
//               fr: 'Actif',
//             },
//             slug: 'actif',
//             text: 'Actif',
//             uid: 'actif',
//           },
//           telephone: null,
//           text: "Plateforme de biologie in vivo de l'IRIC",
//           totalEquipements: 54,
//           typeContenu: 'Infrastructure',
//           typeInfrastructure: {
//             descriptions: {
//               fr: null,
//             },
//             id: 2,
//             nom: 'Plateforme',
//             noms: {
//               fr: 'Plateforme',
//             },
//             slug: 'plateforme',
//             text: 'Plateforme',
//             uid: 'plateforme',
//           },
//           uid: 'ur14388',
//           unite: {
//             acronym: null,
//             acronyms: {
//               fr: null,
//             },
//             createdAt: '2023-10-16T17:59:15-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               fr: null,
//             },
//             id: '652db2331fa8b',
//             lastUpdatedAt: '2023-10-16T17:59:15-0400',
//             names: {
//               fr: 'Faculté de médecine - Département de médecine',
//             },
//             organizationId: 633,
//             parent: {
//               acronym: null,
//               acronyms: {
//                 en: null,
//                 fr: null,
//               },
//               createdAt: '2023-10-16T21:48:30-0400',
//               dateBegin: null,
//               dateEnd: null,
//               descriptions: {
//                 en: null,
//                 fr: null,
//               },
//               id: 'G000001',
//               lastUpdatedAt: '2023-10-16T21:48:30-0400',
//               names: {
//                 en: 'University of Montreal',
//                 fr: 'Université de Montréal',
//               },
//               organizationId: 1,
//               parent: null,
//               pseudonymes: {
//                 en: null,
//                 fr: null,
//               },
//               text: 'Université de Montréal',
//               typeContenu: 'Etablissement',
//               typeEtablissement: {
//                 descriptions: {
//                   fr: null,
//                 },
//                 id: 1,
//                 nom: 'Académique',
//                 noms: {
//                   fr: 'Académique',
//                 },
//                 slug: 'academique',
//                 text: 'Académique',
//                 uid: 'academique',
//               },
//               uid: 'G000001',
//             },
//             pseudonymes: {
//               fr: null,
//             },
//             text: 'Faculté de médecine - Département de médecine',
//             typeContenu: 'Unite',
//             uid: 'D203A0',
//           },
//           url: null,
//           visibilite: {
//             descriptions: {
//               fr: null,
//             },
//             id: 1,
//             nom: 'Public',
//             noms: {
//               fr: 'Public',
//             },
//             slug: 'public',
//             text: 'Public',
//             uid: 'public',
//           },
//         },
//         juridiction: {
//           acronym: 'IRIC',
//           acronyms: {
//             fr: 'IRIC',
//           },
//           createdAt: '2023-10-16T21:56:50-0400',
//           dateBegin: null,
//           dateEnd: null,
//           descriptions: {
//             fr: null,
//           },
//           id: 'A000017',
//           lastUpdatedAt: '2023-10-16T17:59:58-0400',
//           names: {
//             fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//           },
//           organizationId: 10,
//           parent: {
//             acronym: null,
//             acronyms: {
//               en: null,
//               fr: null,
//             },
//             createdAt: '2023-10-16T21:48:30-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               en: null,
//               fr: null,
//             },
//             id: 'G000001',
//             lastUpdatedAt: '2023-10-16T21:48:30-0400',
//             names: {
//               en: 'University of Montreal',
//               fr: 'Université de Montréal',
//             },
//             organizationId: 1,
//             parent: null,
//             pseudonymes: {
//               en: null,
//               fr: null,
//             },
//             text: 'Université de Montréal',
//             typeContenu: 'Etablissement',
//             typeEtablissement: {
//               descriptions: {
//                 fr: null,
//               },
//               id: 1,
//               nom: 'Académique',
//               noms: {
//                 fr: 'Académique',
//               },
//               slug: 'academique',
//               text: 'Académique',
//               uid: 'academique',
//             },
//             uid: 'G000001',
//           },
//           pseudonymes: {
//             fr: null,
//           },
//           text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//           typeContenu: 'Unite',
//           uid: 'ur13772',
//         },
//         lastUpdatedAt: null,
//         locaux: [],
//         maintenance: null,
//         maintenances: {
//           en: null,
//           fr: null,
//         },
//         manufacturier: null,
//         mettreEnVedette: false,
//         modele: null,
//         nom: 'Bedding changing biocabinet, 4ft, mobile, adjustable height',
//         noms: {
//           en: 'Bedding changing biocabinet, 4ft, mobile, adjustable height',
//           fr: 'Bedding changing biocabinet, 4ft, mobile, adjustable height',
//         },
//         numeroHomologation: null,
//         numeroInventaire: null,
//         numeroSerie: null,
//         photos: [],
//         poleExcellences: [],
//         precisions: [],
//         projetFinancements: [],
//         reparateurs: [],
//         responsablesSST: [],
//         responsablesTechnique: [],
//         risque: null,
//         risques: {
//           en: null,
//           fr: null,
//         },
//         secteurApplications: [],
//         statutDecommissionEquipement: {
//           id: 'date-decommission-indef',
//           noms: {
//             en: 'End of support undefined',
//             fr: 'Fin de support indéterminée',
//           },
//           uid: 'date-decommission-indef',
//         },
//         statutDureeVieEquipement: {
//           id: 'duree-vie-indef',
//           noms: {
//             en: 'Unknown Life expectancy',
//             fr: 'Espérance de vie indéterminée',
//           },
//           uid: 'duree-vie-indef',
//         },
//         techniques: [],
//         text: 'Bedding changing biocabinet, 4ft, mobile, adjustable height',
//         titulaire: {
//           created_at: '2023-10-16T18:02:58-0400',
//           emails: [
//             {
//               address: '<EMAIL>',
//               id: 678,
//               text: '<EMAIL>',
//             },
//           ],
//           family_name: 'Sauvageau',
//           given_name: 'Guy',
//           id: '652db312ee859',
//           last_updated_at: '2023-10-16T18:02:58-0400',
//           name_with_lastname_first: 'Sauvageau, Guy',
//           person_id: 765,
//           text: 'Guy Sauvageau',
//           type_contenu: 'Person',
//           uid: 'in14491',
//         },
//         typeContenu: 'Equipement',
//         typeEquipement: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Équipement',
//           noms: {
//             fr: 'Équipement',
//           },
//           slug: 'equipement',
//           text: 'Équipement',
//           uid: 'equipement',
//         },
//         utilisation: null,
//         utilisations: {
//           en: null,
//           fr: null,
//         },
//         videos: [],
//         visibilite: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Public',
//           noms: {
//             fr: 'Public',
//           },
//           slug: 'public',
//           text: 'Public',
//           uid: 'public',
//         },
//       },
//       {
//         _class: 'Rie\\RieCoreBundle\\Entity\\Equipement',
//         _className: 'Equipement',
//         _resourceName: 'equipement',
//         anneeFabrication: null,
//         batimentLocaux: [],
//         campusLocaux: [],
//         categories: [],
//         commentaire: null,
//         commentaires: {
//           en: null,
//           fr: null,
//         },
//         contratServices: [],
//         coutEspece: '11631.00',
//         coutNature: null,
//         coutTotal: 11631,
//         createdAt: '2023-10-17T06:11:33-0400',
//         dateAchat: null,
//         dateDecommission: null,
//         dateFin: null,
//         dateInstallation: null,
//         dateInstalOuAchat: null,
//         defaillance: null,
//         defaillances: {
//           en: null,
//           fr: null,
//         },
//         description: null,
//         descriptions: {
//           en: null,
//           fr: null,
//         },
//         detaillants: [],
//         disposition: null,
//         dispositions: {
//           en: null,
//           fr: null,
//         },
//         documents: [],
//         doi: null,
//         domaineRecherches: [],
//         emplacement: null,
//         equipementAccessoires: [],
//         equipementAutres: [],
//         equipementComposantes: [],
//         equipementDimension: null,
//         equipementDureeVie: null,
//         equipementEmplacement: null,
//         equipementId: 61,
//         equipementLogiciels: [],
//         equipementParents: [],
//         estEssais: false,
//         estMasquer: false,
//         etatEquipement: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Fonctionnel',
//           noms: {
//             fr: 'Fonctionnel',
//           },
//           slug: 'fonctionnel',
//           text: 'Fonctionnel',
//           uid: 'fonctionnel',
//         },
//         financement: null,
//         financements: {
//           en: null,
//           fr: null,
//         },
//         fournisseur: null,
//         frequenceMaintenance: null,
//         id: 'E000000065',
//         ID: 'equipement.E000000065',
//         infrastructure: {
//           acronyme: 'IRIC Biologie in vivo',
//           acronymes: {
//             fr: 'IRIC Biologie in vivo',
//           },
//           batimentLocaux: [],
//           campusLocaux: [],
//           createdAt: '2023-10-17T05:54:30-0400',
//           descriptions: {
//             fr: 'L’institut héberge une des plus grandes animaleries au Canada offrant un secteur exempt de pathogènes, un secteur conventionnel et une quarantaine autonome. En plus de l’hébergement et des supports technique et vétérinaire pour la recherche et la santé animale, l’animalerie de l’institut collabore avec la plateforme de biopharmacie de la Faculté de pharmacie de l’Université de Montréal pour offrir des services de pharmacocinétique, bioanalyse et toxicologie (www.pfbio.ca) .',
//           },
//           directeursTechnique: [
//             {
//               created_at: '2023-10-16T18:02:58-0400',
//               emails: [
//                 {
//                   address: '<EMAIL>',
//                   id: 678,
//                   text: '<EMAIL>',
//                 },
//               ],
//               family_name: 'Sauvageau',
//               given_name: 'Guy',
//               id: '652db312ee859',
//               last_updated_at: '2023-10-16T18:02:58-0400',
//               name_with_lastname_first: 'Sauvageau, Guy',
//               person_id: 765,
//               text: 'Guy Sauvageau',
//               type_contenu: 'Person',
//               uid: 'in14491',
//             },
//           ],
//           emplacement: null,
//           id: 'I000018',
//           infrastructureId: 271,
//           infrastructureReception: null,
//           juridiction: {
//             acronym: 'IRIC',
//             acronyms: {
//               fr: 'IRIC',
//             },
//             createdAt: '2023-10-16T21:56:50-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               fr: null,
//             },
//             id: 'A000017',
//             lastUpdatedAt: '2023-10-16T17:59:58-0400',
//             names: {
//               fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//             },
//             organizationId: 10,
//             parent: {
//               acronym: null,
//               acronyms: {
//                 en: null,
//                 fr: null,
//               },
//               createdAt: '2023-10-16T21:48:30-0400',
//               dateBegin: null,
//               dateEnd: null,
//               descriptions: {
//                 en: null,
//                 fr: null,
//               },
//               id: 'G000001',
//               lastUpdatedAt: '2023-10-16T21:48:30-0400',
//               names: {
//                 en: 'University of Montreal',
//                 fr: 'Université de Montréal',
//               },
//               organizationId: 1,
//               parent: null,
//               pseudonymes: {
//                 en: null,
//                 fr: null,
//               },
//               text: 'Université de Montréal',
//               typeContenu: 'Etablissement',
//               typeEtablissement: {
//                 descriptions: {
//                   fr: null,
//                 },
//                 id: 1,
//                 nom: 'Académique',
//                 noms: {
//                   fr: 'Académique',
//                 },
//                 slug: 'academique',
//                 text: 'Académique',
//                 uid: 'academique',
//               },
//               uid: 'G000001',
//             },
//             pseudonymes: {
//               fr: null,
//             },
//             text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//             typeContenu: 'Unite',
//             uid: 'ur13772',
//           },
//           laboratoireInnovations: [],
//           lastUpdatedAt: '2023-10-17T06:35:35-0400',
//           locaux: [],
//           mettreEnVedette: false,
//           nom: "Plateforme de biologie in vivo de l'IRIC",
//           noms: {
//             fr: "Plateforme de biologie in vivo de l'IRIC",
//           },
//           projetFinancements: [],
//           pseudonyme:
//             'Institut de recherche en immunologie et en cancérologie - animalerie. IRIC animalerie',
//           responsablesSST: [],
//           responsablesTechnique: [
//             {
//               created_at: '2023-10-17T05:58:19-0400',
//               emails: [
//                 {
//                   address: '<EMAIL>',
//                   id: 7600,
//                   text: '<EMAIL>',
//                 },
//               ],
//               family_name: 'Julie',
//               given_name: 'Gervais',
//               id: '652e5abb9f702',
//               last_updated_at: null,
//               name_with_lastname_first: 'Julie, Gervais',
//               person_id: 9221,
//               text: 'Gervais Julie',
//               type_contenu: 'Person',
//               uid: null,
//             },
//           ],
//           statutInfrastructure: {
//             descriptions: {
//               fr: null,
//             },
//             id: 2,
//             nom: 'Actif',
//             noms: {
//               fr: 'Actif',
//             },
//             slug: 'actif',
//             text: 'Actif',
//             uid: 'actif',
//           },
//           telephone: null,
//           text: "Plateforme de biologie in vivo de l'IRIC",
//           totalEquipements: 54,
//           typeContenu: 'Infrastructure',
//           typeInfrastructure: {
//             descriptions: {
//               fr: null,
//             },
//             id: 2,
//             nom: 'Plateforme',
//             noms: {
//               fr: 'Plateforme',
//             },
//             slug: 'plateforme',
//             text: 'Plateforme',
//             uid: 'plateforme',
//           },
//           uid: 'ur14388',
//           unite: {
//             acronym: null,
//             acronyms: {
//               fr: null,
//             },
//             createdAt: '2023-10-16T17:59:15-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               fr: null,
//             },
//             id: '652db2331fa8b',
//             lastUpdatedAt: '2023-10-16T17:59:15-0400',
//             names: {
//               fr: 'Faculté de médecine - Département de médecine',
//             },
//             organizationId: 633,
//             parent: {
//               acronym: null,
//               acronyms: {
//                 en: null,
//                 fr: null,
//               },
//               createdAt: '2023-10-16T21:48:30-0400',
//               dateBegin: null,
//               dateEnd: null,
//               descriptions: {
//                 en: null,
//                 fr: null,
//               },
//               id: 'G000001',
//               lastUpdatedAt: '2023-10-16T21:48:30-0400',
//               names: {
//                 en: 'University of Montreal',
//                 fr: 'Université de Montréal',
//               },
//               organizationId: 1,
//               parent: null,
//               pseudonymes: {
//                 en: null,
//                 fr: null,
//               },
//               text: 'Université de Montréal',
//               typeContenu: 'Etablissement',
//               typeEtablissement: {
//                 descriptions: {
//                   fr: null,
//                 },
//                 id: 1,
//                 nom: 'Académique',
//                 noms: {
//                   fr: 'Académique',
//                 },
//                 slug: 'academique',
//                 text: 'Académique',
//                 uid: 'academique',
//               },
//               uid: 'G000001',
//             },
//             pseudonymes: {
//               fr: null,
//             },
//             text: 'Faculté de médecine - Département de médecine',
//             typeContenu: 'Unite',
//             uid: 'D203A0',
//           },
//           url: null,
//           visibilite: {
//             descriptions: {
//               fr: null,
//             },
//             id: 1,
//             nom: 'Public',
//             noms: {
//               fr: 'Public',
//             },
//             slug: 'public',
//             text: 'Public',
//             uid: 'public',
//           },
//         },
//         juridiction: {
//           acronym: 'IRIC',
//           acronyms: {
//             fr: 'IRIC',
//           },
//           createdAt: '2023-10-16T21:56:50-0400',
//           dateBegin: null,
//           dateEnd: null,
//           descriptions: {
//             fr: null,
//           },
//           id: 'A000017',
//           lastUpdatedAt: '2023-10-16T17:59:58-0400',
//           names: {
//             fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//           },
//           organizationId: 10,
//           parent: {
//             acronym: null,
//             acronyms: {
//               en: null,
//               fr: null,
//             },
//             createdAt: '2023-10-16T21:48:30-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               en: null,
//               fr: null,
//             },
//             id: 'G000001',
//             lastUpdatedAt: '2023-10-16T21:48:30-0400',
//             names: {
//               en: 'University of Montreal',
//               fr: 'Université de Montréal',
//             },
//             organizationId: 1,
//             parent: null,
//             pseudonymes: {
//               en: null,
//               fr: null,
//             },
//             text: 'Université de Montréal',
//             typeContenu: 'Etablissement',
//             typeEtablissement: {
//               descriptions: {
//                 fr: null,
//               },
//               id: 1,
//               nom: 'Académique',
//               noms: {
//                 fr: 'Académique',
//               },
//               slug: 'academique',
//               text: 'Académique',
//               uid: 'academique',
//             },
//             uid: 'G000001',
//           },
//           pseudonymes: {
//             fr: null,
//           },
//           text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//           typeContenu: 'Unite',
//           uid: 'ur13772',
//         },
//         lastUpdatedAt: null,
//         locaux: [],
//         maintenance: null,
//         maintenances: {
//           en: null,
//           fr: null,
//         },
//         manufacturier: null,
//         mettreEnVedette: false,
//         modele: null,
//         nom: 'Bedding changing biocabinet, 6ft, mobile, adjustable height',
//         noms: {
//           en: 'Bedding changing biocabinet, 6ft, mobile, adjustable height',
//           fr: 'Bedding changing biocabinet, 6ft, mobile, adjustable height',
//         },
//         numeroHomologation: null,
//         numeroInventaire: null,
//         numeroSerie: null,
//         photos: [],
//         poleExcellences: [],
//         precisions: [],
//         projetFinancements: [],
//         reparateurs: [],
//         responsablesSST: [],
//         responsablesTechnique: [],
//         risque: null,
//         risques: {
//           en: null,
//           fr: null,
//         },
//         secteurApplications: [],
//         statutDecommissionEquipement: {
//           id: 'date-decommission-indef',
//           noms: {
//             en: 'End of support undefined',
//             fr: 'Fin de support indéterminée',
//           },
//           uid: 'date-decommission-indef',
//         },
//         statutDureeVieEquipement: {
//           id: 'duree-vie-indef',
//           noms: {
//             en: 'Unknown Life expectancy',
//             fr: 'Espérance de vie indéterminée',
//           },
//           uid: 'duree-vie-indef',
//         },
//         techniques: [],
//         text: 'Bedding changing biocabinet, 6ft, mobile, adjustable height',
//         titulaire: {
//           created_at: '2023-10-16T18:02:58-0400',
//           emails: [
//             {
//               address: '<EMAIL>',
//               id: 678,
//               text: '<EMAIL>',
//             },
//           ],
//           family_name: 'Sauvageau',
//           given_name: 'Guy',
//           id: '652db312ee859',
//           last_updated_at: '2023-10-16T18:02:58-0400',
//           name_with_lastname_first: 'Sauvageau, Guy',
//           person_id: 765,
//           text: 'Guy Sauvageau',
//           type_contenu: 'Person',
//           uid: 'in14491',
//         },
//         typeContenu: 'Equipement',
//         typeEquipement: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Équipement',
//           noms: {
//             fr: 'Équipement',
//           },
//           slug: 'equipement',
//           text: 'Équipement',
//           uid: 'equipement',
//         },
//         utilisation: null,
//         utilisations: {
//           en: null,
//           fr: null,
//         },
//         videos: [],
//         visibilite: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Public',
//           noms: {
//             fr: 'Public',
//           },
//           slug: 'public',
//           text: 'Public',
//           uid: 'public',
//         },
//       },
//       {
//         _class: 'Rie\\RieCoreBundle\\Entity\\Equipement',
//         _className: 'Equipement',
//         _resourceName: 'equipement',
//         anneeFabrication: null,
//         batimentLocaux: [],
//         campusLocaux: [],
//         categories: [],
//         commentaire: null,
//         commentaires: {
//           en: null,
//           fr: null,
//         },
//         contratServices: [],
//         coutEspece: '749578.00',
//         coutNature: null,
//         coutTotal: 749578,
//         createdAt: '2023-10-17T06:11:33-0400',
//         dateAchat: null,
//         dateDecommission: null,
//         dateFin: null,
//         dateInstallation: null,
//         dateInstalOuAchat: null,
//         defaillance: null,
//         defaillances: {
//           en: null,
//           fr: null,
//         },
//         description: null,
//         descriptions: {
//           en: null,
//           fr: null,
//         },
//         detaillants: [],
//         disposition: null,
//         dispositions: {
//           en: null,
//           fr: null,
//         },
//         documents: [],
//         doi: null,
//         domaineRecherches: [],
//         emplacement: null,
//         equipementAccessoires: [],
//         equipementAutres: [],
//         equipementComposantes: [],
//         equipementDimension: null,
//         equipementDureeVie: null,
//         equipementEmplacement: null,
//         equipementId: 62,
//         equipementLogiciels: [],
//         equipementParents: [],
//         estEssais: false,
//         estMasquer: false,
//         etatEquipement: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Fonctionnel',
//           noms: {
//             fr: 'Fonctionnel',
//           },
//           slug: 'fonctionnel',
//           text: 'Fonctionnel',
//           uid: 'fonctionnel',
//         },
//         financement: null,
//         financements: {
//           en: null,
//           fr: null,
//         },
//         fournisseur: null,
//         frequenceMaintenance: null,
//         id: 'E000000066',
//         ID: 'equipement.E000000066',
//         infrastructure: {
//           acronyme: 'IRIC Biologie in vivo',
//           acronymes: {
//             fr: 'IRIC Biologie in vivo',
//           },
//           batimentLocaux: [],
//           campusLocaux: [],
//           createdAt: '2023-10-17T05:54:30-0400',
//           descriptions: {
//             fr: 'L’institut héberge une des plus grandes animaleries au Canada offrant un secteur exempt de pathogènes, un secteur conventionnel et une quarantaine autonome. En plus de l’hébergement et des supports technique et vétérinaire pour la recherche et la santé animale, l’animalerie de l’institut collabore avec la plateforme de biopharmacie de la Faculté de pharmacie de l’Université de Montréal pour offrir des services de pharmacocinétique, bioanalyse et toxicologie (www.pfbio.ca) .',
//           },
//           directeursTechnique: [
//             {
//               created_at: '2023-10-16T18:02:58-0400',
//               emails: [
//                 {
//                   address: '<EMAIL>',
//                   id: 678,
//                   text: '<EMAIL>',
//                 },
//               ],
//               family_name: 'Sauvageau',
//               given_name: 'Guy',
//               id: '652db312ee859',
//               last_updated_at: '2023-10-16T18:02:58-0400',
//               name_with_lastname_first: 'Sauvageau, Guy',
//               person_id: 765,
//               text: 'Guy Sauvageau',
//               type_contenu: 'Person',
//               uid: 'in14491',
//             },
//           ],
//           emplacement: null,
//           id: 'I000018',
//           infrastructureId: 271,
//           infrastructureReception: null,
//           juridiction: {
//             acronym: 'IRIC',
//             acronyms: {
//               fr: 'IRIC',
//             },
//             createdAt: '2023-10-16T21:56:50-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               fr: null,
//             },
//             id: 'A000017',
//             lastUpdatedAt: '2023-10-16T17:59:58-0400',
//             names: {
//               fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//             },
//             organizationId: 10,
//             parent: {
//               acronym: null,
//               acronyms: {
//                 en: null,
//                 fr: null,
//               },
//               createdAt: '2023-10-16T21:48:30-0400',
//               dateBegin: null,
//               dateEnd: null,
//               descriptions: {
//                 en: null,
//                 fr: null,
//               },
//               id: 'G000001',
//               lastUpdatedAt: '2023-10-16T21:48:30-0400',
//               names: {
//                 en: 'University of Montreal',
//                 fr: 'Université de Montréal',
//               },
//               organizationId: 1,
//               parent: null,
//               pseudonymes: {
//                 en: null,
//                 fr: null,
//               },
//               text: 'Université de Montréal',
//               typeContenu: 'Etablissement',
//               typeEtablissement: {
//                 descriptions: {
//                   fr: null,
//                 },
//                 id: 1,
//                 nom: 'Académique',
//                 noms: {
//                   fr: 'Académique',
//                 },
//                 slug: 'academique',
//                 text: 'Académique',
//                 uid: 'academique',
//               },
//               uid: 'G000001',
//             },
//             pseudonymes: {
//               fr: null,
//             },
//             text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//             typeContenu: 'Unite',
//             uid: 'ur13772',
//           },
//           laboratoireInnovations: [],
//           lastUpdatedAt: '2023-10-17T06:35:35-0400',
//           locaux: [],
//           mettreEnVedette: false,
//           nom: "Plateforme de biologie in vivo de l'IRIC",
//           noms: {
//             fr: "Plateforme de biologie in vivo de l'IRIC",
//           },
//           projetFinancements: [],
//           pseudonyme:
//             'Institut de recherche en immunologie et en cancérologie - animalerie. IRIC animalerie',
//           responsablesSST: [],
//           responsablesTechnique: [
//             {
//               created_at: '2023-10-17T05:58:19-0400',
//               emails: [
//                 {
//                   address: '<EMAIL>',
//                   id: 7600,
//                   text: '<EMAIL>',
//                 },
//               ],
//               family_name: 'Julie',
//               given_name: 'Gervais',
//               id: '652e5abb9f702',
//               last_updated_at: null,
//               name_with_lastname_first: 'Julie, Gervais',
//               person_id: 9221,
//               text: 'Gervais Julie',
//               type_contenu: 'Person',
//               uid: null,
//             },
//           ],
//           statutInfrastructure: {
//             descriptions: {
//               fr: null,
//             },
//             id: 2,
//             nom: 'Actif',
//             noms: {
//               fr: 'Actif',
//             },
//             slug: 'actif',
//             text: 'Actif',
//             uid: 'actif',
//           },
//           telephone: null,
//           text: "Plateforme de biologie in vivo de l'IRIC",
//           totalEquipements: 54,
//           typeContenu: 'Infrastructure',
//           typeInfrastructure: {
//             descriptions: {
//               fr: null,
//             },
//             id: 2,
//             nom: 'Plateforme',
//             noms: {
//               fr: 'Plateforme',
//             },
//             slug: 'plateforme',
//             text: 'Plateforme',
//             uid: 'plateforme',
//           },
//           uid: 'ur14388',
//           unite: {
//             acronym: null,
//             acronyms: {
//               fr: null,
//             },
//             createdAt: '2023-10-16T17:59:15-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               fr: null,
//             },
//             id: '652db2331fa8b',
//             lastUpdatedAt: '2023-10-16T17:59:15-0400',
//             names: {
//               fr: 'Faculté de médecine - Département de médecine',
//             },
//             organizationId: 633,
//             parent: {
//               acronym: null,
//               acronyms: {
//                 en: null,
//                 fr: null,
//               },
//               createdAt: '2023-10-16T21:48:30-0400',
//               dateBegin: null,
//               dateEnd: null,
//               descriptions: {
//                 en: null,
//                 fr: null,
//               },
//               id: 'G000001',
//               lastUpdatedAt: '2023-10-16T21:48:30-0400',
//               names: {
//                 en: 'University of Montreal',
//                 fr: 'Université de Montréal',
//               },
//               organizationId: 1,
//               parent: null,
//               pseudonymes: {
//                 en: null,
//                 fr: null,
//               },
//               text: 'Université de Montréal',
//               typeContenu: 'Etablissement',
//               typeEtablissement: {
//                 descriptions: {
//                   fr: null,
//                 },
//                 id: 1,
//                 nom: 'Académique',
//                 noms: {
//                   fr: 'Académique',
//                 },
//                 slug: 'academique',
//                 text: 'Académique',
//                 uid: 'academique',
//               },
//               uid: 'G000001',
//             },
//             pseudonymes: {
//               fr: null,
//             },
//             text: 'Faculté de médecine - Département de médecine',
//             typeContenu: 'Unite',
//             uid: 'D203A0',
//           },
//           url: null,
//           visibilite: {
//             descriptions: {
//               fr: null,
//             },
//             id: 1,
//             nom: 'Public',
//             noms: {
//               fr: 'Public',
//             },
//             slug: 'public',
//             text: 'Public',
//             uid: 'public',
//           },
//         },
//         juridiction: {
//           acronym: 'IRIC',
//           acronyms: {
//             fr: 'IRIC',
//           },
//           createdAt: '2023-10-16T21:56:50-0400',
//           dateBegin: null,
//           dateEnd: null,
//           descriptions: {
//             fr: null,
//           },
//           id: 'A000017',
//           lastUpdatedAt: '2023-10-16T17:59:58-0400',
//           names: {
//             fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//           },
//           organizationId: 10,
//           parent: {
//             acronym: null,
//             acronyms: {
//               en: null,
//               fr: null,
//             },
//             createdAt: '2023-10-16T21:48:30-0400',
//             dateBegin: null,
//             dateEnd: null,
//             descriptions: {
//               en: null,
//               fr: null,
//             },
//             id: 'G000001',
//             lastUpdatedAt: '2023-10-16T21:48:30-0400',
//             names: {
//               en: 'University of Montreal',
//               fr: 'Université de Montréal',
//             },
//             organizationId: 1,
//             parent: null,
//             pseudonymes: {
//               en: null,
//               fr: null,
//             },
//             text: 'Université de Montréal',
//             typeContenu: 'Etablissement',
//             typeEtablissement: {
//               descriptions: {
//                 fr: null,
//               },
//               id: 1,
//               nom: 'Académique',
//               noms: {
//                 fr: 'Académique',
//               },
//               slug: 'academique',
//               text: 'Académique',
//               uid: 'academique',
//             },
//             uid: 'G000001',
//           },
//           pseudonymes: {
//             fr: null,
//           },
//           text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
//           typeContenu: 'Unite',
//           uid: 'ur13772',
//         },
//         lastUpdatedAt: null,
//         locaux: [],
//         maintenance: null,
//         maintenances: {
//           en: null,
//           fr: null,
//         },
//         manufacturier: null,
//         mettreEnVedette: false,
//         modele: null,
//         nom: 'Emergency power supply (1000KW, rooftop unit)',
//         noms: {
//           en: 'Emergency power supply (1000KW, rooftop unit)',
//           fr: 'Emergency power supply (1000KW, rooftop unit)',
//         },
//         numeroHomologation: null,
//         numeroInventaire: null,
//         numeroSerie: null,
//         photos: [],
//         poleExcellences: [],
//         precisions: [],
//         projetFinancements: [],
//         reparateurs: [],
//         responsablesSST: [],
//         responsablesTechnique: [],
//         risque: null,
//         risques: {
//           en: null,
//           fr: null,
//         },
//         secteurApplications: [],
//         statutDecommissionEquipement: {
//           id: 'date-decommission-indef',
//           noms: {
//             en: 'End of support undefined',
//             fr: 'Fin de support indéterminée',
//           },
//           uid: 'date-decommission-indef',
//         },
//         statutDureeVieEquipement: {
//           id: 'duree-vie-indef',
//           noms: {
//             en: 'Unknown Life expectancy',
//             fr: 'Espérance de vie indéterminée',
//           },
//           uid: 'duree-vie-indef',
//         },
//         techniques: [],
//         text: 'Emergency power supply (1000KW, rooftop unit)',
//         titulaire: {
//           created_at: '2023-10-16T18:02:58-0400',
//           emails: [
//             {
//               address: '<EMAIL>',
//               id: 678,
//               text: '<EMAIL>',
//             },
//           ],
//           family_name: 'Sauvageau',
//           given_name: 'Guy',
//           id: '652db312ee859',
//           last_updated_at: '2023-10-16T18:02:58-0400',
//           name_with_lastname_first: 'Sauvageau, Guy',
//           person_id: 765,
//           text: 'Guy Sauvageau',
//           type_contenu: 'Person',
//           uid: 'in14491',
//         },
//         typeContenu: 'Equipement',
//         typeEquipement: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Équipement',
//           noms: {
//             fr: 'Équipement',
//           },
//           slug: 'equipement',
//           text: 'Équipement',
//           uid: 'equipement',
//         },
//         utilisation: null,
//         utilisations: {
//           en: null,
//           fr: null,
//         },
//         videos: [],
//         visibilite: {
//           descriptions: {
//             fr: null,
//           },
//           id: 1,
//           nom: 'Public',
//           noms: {
//             fr: 'Public',
//           },
//           slug: 'public',
//           text: 'Public',
//           uid: 'public',
//         },
//       },
//     ],
//   },
// ];
