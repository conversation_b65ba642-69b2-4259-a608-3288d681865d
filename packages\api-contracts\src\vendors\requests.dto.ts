import * as Schema from 'effect/Schema';
import { OptionalLocalizedFieldDtoSchema } from '../common';

/**
 * Describes the entire request body for creating or updating a vendor.
 * This is the shape of the data the client will send to the API.
 **/

export const VendorFormRequestDtoSchema = Schema.Struct({
  id: Schema.optional(Schema.String),
  isActive: Schema.optional(Schema.Boolean),
  startDate: Schema.optional(Schema.NullOr(Schema.String)),
  endDate: Schema.optional(Schema.NullOr(Schema.String)),
  translations: Schema.Array(
    Schema.Struct({
      locale: Schema.String,
      name: Schema.String,
      website: Schema.optional(Schema.String),
      description: Schema.optional(Schema.String),
      otherNames: Schema.optional(Schema.String),
    }),
  ),
});

export type VendorFormRequestDTO = Schema.Schema.Type<
  typeof VendorFormRequestDtoSchema
>;

/**
 * DTO for vendor creation requests
 */
export const VendorCreateRequestDtoSchema = Schema.Struct({
  isActive: Schema.optional(Schema.Boolean),
  startDate: Schema.optional(Schema.NullOr(Schema.String)),
  endDate: Schema.optional(Schema.NullOr(Schema.String)),
  translations: Schema.Array(OptionalLocalizedFieldDtoSchema),
});

export type VendorCreateRequestDTO = Schema.Schema.Type<
  typeof VendorCreateRequestDtoSchema
>;

/**
 * DTO for vendor update requests
 */
export const VendorUpdateRequestDtoSchema = Schema.Struct({
  isActive: Schema.Boolean,
  startDate: Schema.optional(Schema.NullOr(Schema.String)),
  endDate: Schema.optional(Schema.NullOr(Schema.String)),
  translations: Schema.Array(OptionalLocalizedFieldDtoSchema),
});

export type VendorUpdateRequestDTO = Schema.Schema.Type<
  typeof VendorUpdateRequestDtoSchema
>;
