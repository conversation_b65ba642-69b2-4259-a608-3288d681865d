import type { InfrastructureFull } from '@/types/infrastructure';

export const infrastructureMocks: InfrastructureFull[] = [
  {
    batimentLocaux: [
      {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '2',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Roger-Gaudry',
        noms: {
          fr: 'Roger-Gaudry',
        },
        pseudonyme: null,
        slug: null,
        text: 'Roger-Gaudry - Campus principal',
        uid: '511A',
      },
    ],
    campusLocaux: [
      {
        etablissement: {
          id: 'G000001',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          text: 'Université de Montréal',
          uid: 'G000001',
        },
        id: '1',
        nom: 'Campus principal',
        noms: {
          fr: 'Campus principal',
        },
        pseudonyme: null,
        slug: 'campus-principal',
        text: 'Campus principal',
        uid: 'mtl',
      },
    ],
    createdAt: '2023-11-02T18:07:35-0400',
    descriptions: {
      en: null,
      fr: null,
    },
    directeursTechnique: [
      {
        createdAt: '2023-11-01T12:18:46-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 4035,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Pascal',
        givenName: 'John',
        id: '65427a66d29a6',
        lastUpdatedAt: '2023-11-01T12:18:46-0400',
        name_with_lastname_first: 'Pascal, John',
        personId: 4672,
        text: 'John Pascal',
        type_contenu: 'Person',
        uid: 'in19808',
      },
    ],
    emplacement: {
      batiment: {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '2',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Roger-Gaudry',
        noms: {
          fr: 'Roger-Gaudry',
        },
        pseudonyme: null,
        slug: null,
        text: 'Roger-Gaudry - Campus principal',
        uid: '511A',
      },
      estLocal: true,
      id: 2,
      local: {
        batiment: {
          campus: {
            etablissement: {
              id: 'G000001',
              names: {
                en: 'University of Montreal',
                fr: 'Université de Montréal',
              },

              text: 'Université de Montréal',

              uid: 'G000001',
            },
            id: '1',
            nom: 'Campus principal',
            noms: {
              fr: 'Campus principal',
            },
            pseudonyme: null,
            slug: 'campus-principal',
            text: 'Campus principal',
            uid: 'mtl',
          },
          id: '2',
          juridiction: {
            acronym: null,
            acronyms: {
              en: null,
              fr: null,
            },
            createdAt: '2023-11-01T11:28:35-0400',
            dateBegin: null,
            dateEnd: null,
            descriptions: {
              en: null,
              fr: null,
            },
            id: 'G000001',
            lastUpdatedAt: '2023-11-01T11:28:35-0400',
            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            organizationId: 1,
            parent: null,
            pseudonymes: {
              en: null,
              fr: null,
            },
            text: 'Université de Montréal',
            typeContenu: 'Etablissement',
            typeEtablissement: {
              descriptions: {
                fr: null,
              },
              id: 1,
              nom: 'Académique',
              noms: {
                fr: 'Académique',
              },
              slug: 'academique',
              text: 'Académique',
              uid: 'academique',
            },
            uid: 'G000001',
          },
          nom: 'Roger-Gaudry',
          noms: {
            fr: 'Roger-Gaudry',
          },
          pseudonyme: null,
          slug: null,
          text: 'Roger-Gaudry - Campus principal',
          uid: '511A',
        },
        id: 1585,
        juridiction: {
          typeEtablissement: {
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          acronym: null,
          acronyms: {
            fr: null,
          },
          createdAt: '2023-11-01T13:54:02-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            fr: null,
          },
          id: '654290ba2e747',
          lastUpdatedAt: null,
          names: {
            fr: 'Biochimie',
          },
          organizationId: 1299,
          parent: null,
          pseudonymes: {
            fr: null,
          },
          text: 'Biochimie',
          typeContenu: 'Unite',
          uid: '3016',
        },
        numero: 'B-313',
        pseudonyme: null,
        slug: null,
        text: 'B-313 - Roger-Gaudry - Campus principal',
      },
      territory: null,
      text: 'B-313 - Roger-Gaudry - Campus principal',
    },
    id: 'I000260',
    ID: 'infrastructure.I000260',
    infrastructureId: 17,
    infrastructureReception: {
      batiment: {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '2',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Roger-Gaudry',
        noms: {
          fr: 'Roger-Gaudry',
        },
        pseudonyme: null,
        slug: null,
        text: 'Roger-Gaudry - Campus principal',
        uid: '511A',
      },
      estLocal: true,
      id: 2,
      local: {
        batiment: {
          campus: {
            etablissement: {
              id: 'G000001',
              names: {
                en: 'University of Montreal',
                fr: 'Université de Montréal',
              },
              text: 'Université de Montréal',
              uid: 'G000001',
            },
            id: '1',
            nom: 'Campus principal',
            noms: {
              fr: 'Campus principal',
            },
            pseudonyme: null,
            slug: 'campus-principal',
            text: 'Campus principal',
            uid: 'mtl',
          },
          id: '2',
          juridiction: {
            acronym: null,
            acronyms: {
              en: null,
              fr: null,
            },
            createdAt: '2023-11-01T11:28:35-0400',
            dateBegin: null,
            dateEnd: null,
            descriptions: {
              en: null,
              fr: null,
            },
            id: 'G000001',
            lastUpdatedAt: '2023-11-01T11:28:35-0400',
            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            organizationId: 1,
            parent: null,
            pseudonymes: {
              en: null,
              fr: null,
            },
            text: 'Université de Montréal',
            typeContenu: 'Etablissement',
            typeEtablissement: {
              descriptions: {
                fr: null,
              },
              id: 1,
              nom: 'Académique',
              noms: {
                fr: 'Académique',
              },
              slug: 'academique',
              text: 'Académique',
              uid: 'academique',
            },
            uid: 'G000001',
          },
          nom: 'Roger-Gaudry',
          noms: {
            fr: 'Roger-Gaudry',
          },
          pseudonyme: null,
          slug: null,
          text: 'Roger-Gaudry - Campus principal',
          uid: '511A',
        },
        id: 1585,
        juridiction: {
          typeEtablissement: {
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          acronym: null,
          acronyms: {
            fr: null,
          },
          createdAt: '2023-11-01T13:54:02-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            fr: null,
          },
          id: '654290ba2e747',
          lastUpdatedAt: null,
          names: {
            fr: 'Biochimie',
          },
          organizationId: 1299,
          parent: null,
          pseudonymes: {
            fr: null,
          },
          text: 'Biochimie',
          typeContenu: 'Unite',
          uid: '3016',
        },
        numero: 'B-313',
        pseudonyme: null,
        slug: null,
        text: 'B-313 - Roger-Gaudry - Campus principal',
      },
      territory: null,
      text: 'B-313 - Roger-Gaudry - Campus principal',
    },
    juridiction: {
      acronym: null,
      acronyms: {
        en: null,
        fr: null,
      },
      createdAt: '2023-11-01T11:28:35-0400',
      dateBegin: null,
      dateEnd: null,
      descriptions: {
        en: null,
        fr: null,
      },
      id: 'G000001',
      lastUpdatedAt: '2023-11-01T11:28:35-0400',
      names: {
        en: 'University of Montreal',
        fr: 'Université de Montréal',
      },
      organizationId: 1,
      parent: null,
      pseudonymes: {
        en: null,
        fr: null,
      },
      text: 'Université de Montréal',
      typeContenu: 'Etablissement',
      typeEtablissement: {
        descriptions: {
          fr: null,
        },
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      uid: 'G000001',
    },
    laboratoireInnovations: [],
    lastUpdatedAt: '2023-08-02T18:07:35-0400',
    locaux: [],
    mettreEnVedette: false,
    nom: 'Plateforme de biophysique',
    noms: {
      en: 'biophysic',
      fr: 'Plateforme de biophysique',
    },
    projetFinancements: [],
    pseudonyme: 'BMM-Biophysique',
    responsablesSST: [],
    responsablesTechnique: [
      {
        createdAt: '2023-11-02T18:09:12-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 7517,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Cyr',
        givenName: 'Normand',
        id: '65441e085b2c2',
        lastUpdatedAt: null,
        name_with_lastname_first: 'Cyr, Normand',
        personId: 9138,
        text: 'Normand Cyr',
        type_contenu: 'Person',
        uid: null,
      },
      {
        createdAt: '2023-11-02T18:09:12-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 7518,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Lampron',
        givenName: 'Philipe',
        id: '65441e085bbf2',
        lastUpdatedAt: null,
        name_with_lastname_first: 'Lampron, Philipe',
        personId: 9139,
        text: 'Philipe Lampron',
        type_contenu: 'Person',
        uid: null,
      },
      {
        createdAt: '2023-11-02T18:09:12-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 7519,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Truche',
        givenName: 'Sebastien',
        id: '65441e085c692',
        lastUpdatedAt: null,
        name_with_lastname_first: 'Truche, Sebastien',
        personId: 9140,
        text: 'Sebastien Truche',
        type_contenu: 'Person',
        uid: null,
      },
    ],
    statutInfrastructure: {
      descriptions: {
        fr: null,
      },
      id: 2,
      nom: 'Actif',
      noms: {
        fr: 'Actif',
      },
      slug: 'actif',
      text: 'Démantelé',
      uid: 'demantele',
    },
    telephone: null,
    text: 'Plateforme de biophysique',
    totalEquipements: 3,
    typeContenu: 'Infrastructure',
    typeInfrastructure: {
      descriptions: {
        fr: null,
      },
      id: 2,
      nom: 'Plateforme',
      noms: {
        fr: 'Plateforme',
      },
      slug: 'plateforme',
      text: 'Plateforme',
      uid: 'plateforme',
    },
    uid: null,
    unite: {
      typeUnite: {
        descriptions: {
          fr: null,
        },
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      acronym: null,
      acronyms: {
        fr: null,
      },
      createdAt: '2023-11-01T11:41:50-0400',
      dateBegin: null,
      dateEnd: null,
      descriptions: {
        fr: null,
      },
      id: '654271beb6617',
      lastUpdatedAt: '2023-11-01T11:41:50-0400',
      names: {
        fr: 'Faculté de médecine - Département de biochimie et médecine moléculaire',
      },
      organizationId: 627,
      parent: {
        acronym: null,
        acronyms: {
          en: null,
          fr: null,
        },
        createdAt: '2023-11-01T11:28:35-0400',
        dateBegin: null,
        dateEnd: null,
        descriptions: {
          en: null,
          fr: null,
        },
        id: 'G000001',
        lastUpdatedAt: '2023-11-01T11:28:35-0400',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        organizationId: 1,
        parent: null,
        pseudonymes: {
          en: null,
          fr: null,
        },
        text: 'Université de Montréal',
        typeContenu: 'Etablissement',
        typeUnite: {
          descriptions: {
            fr: null,
          },
          id: 1,
          nom: 'Académique',
          noms: {
            fr: 'Académique',
          },
          slug: 'academique',
          text: 'Académique',
          uid: 'academique',
        },
        uid: 'G000001',
      },
      pseudonymes: {
        fr: null,
      },
      text: 'Faculté de médecine - Département de biochimie et médecine moléculaire',
      typeContenu: 'Unite',
      uid: 'D501A0',
    },
    url: 'https://biochimie.umontreal.ca/plateformes-scientifiques-bmm/biophysique/',
    visibilite: {
      descriptions: {
        fr: null,
      },
      id: 1,
      nom: 'Public',
      noms: {
        fr: 'Public',
      },
      slug: 'public',
      text: 'Public',
      uid: 'public',
    },
  },
  {
    batimentLocaux: [
      {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '2',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Roger-Gaudry',
        noms: {
          fr: 'Roger-Gaudry',
        },
        pseudonyme: null,
        slug: null,
        text: 'Roger-Gaudry - Campus principal',
        uid: '511A',
      },
    ],
    campusLocaux: [
      {
        etablissement: {
          id: 'G000001',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          text: 'Université de Montréal',
          uid: 'G000001',
        },
        id: '1',
        nom: 'Campus principal',
        noms: {
          fr: 'Campus principal',
        },
        pseudonyme: null,
        slug: 'campus-principal',
        text: 'Campus principal',
        uid: 'mtl',
      },
    ],
    createdAt: '2023-11-02T18:07:35-0400',
    descriptions: {
      en: null,
      fr: null,
    },
    directeursTechnique: [
      {
        createdAt: '2023-11-01T11:46:54-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 743,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Legault',
        givenName: 'Pascale',
        id: '654272ee9c435',
        lastUpdatedAt: '2023-11-01T11:46:54-0400',
        name_with_lastname_first: 'Legault, Pascale',
        personId: 835,
        text: 'Pascale Legault',
        type_contenu: 'Person',
        uid: 'in14566',
      },
    ],
    emplacement: {
      batiment: {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '2',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Roger-Gaudry',
        noms: {
          fr: 'Roger-Gaudry',
        },
        pseudonyme: null,
        slug: null,
        text: 'Roger-Gaudry - Campus principal',
        uid: '511A',
      },
      estLocal: true,
      id: 2,
      local: null,
      territory: null,
      text: 'Roger-Gaudry - Campus principal',
    },
    id: 'I000385',
    ID: 'infrastructure.I000385',
    infrastructureId: 18,
    infrastructureReception: {
      batiment: {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '2',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Roger-Gaudry',
        noms: {
          fr: 'Roger-Gaudry',
        },
        pseudonyme: null,
        slug: null,
        text: 'Roger-Gaudry - Campus principal',
        uid: '511A',
      },
      estLocal: true,
      id: 2,
      local: null,
      territory: null,
      text: 'Roger-Gaudry - Campus principal',
    },
    juridiction: {
      acronym: null,
      acronyms: {
        en: null,
        fr: null,
      },
      createdAt: '2023-11-01T11:28:35-0400',
      dateBegin: null,
      dateEnd: null,
      descriptions: {
        en: null,
        fr: null,
      },
      id: 'G000001',
      lastUpdatedAt: '2023-11-01T11:28:35-0400',
      names: {
        en: 'University of Montreal',
        fr: 'Université de Montréal',
      },
      organizationId: 1,
      parent: null,
      pseudonymes: {
        en: null,
        fr: null,
      },
      text: 'Université de Montréal',
      typeContenu: 'Etablissement',
      typeEtablissement: {
        descriptions: {
          fr: null,
        },
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      uid: 'G000001',
    },
    laboratoireInnovations: [],
    lastUpdatedAt: '2023-11-02T18:07:35-0400',
    locaux: [],
    mettreEnVedette: false,
    nom: "Plateforme d'imagerie pour gels et blots",
    noms: {
      en: 'gels & blots',
      fr: "Plateforme d'imagerie pour gels et blots",
    },
    projetFinancements: [],
    pseudonyme: 'BMM-Imagerie de gels et blots',
    responsablesSST: [],
    responsablesTechnique: [
      {
        createdAt: '2023-11-02T18:09:12-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 7520,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Bessette',
        givenName: 'Benoit',
        id: '65441e089518c',
        lastUpdatedAt: null,
        name_with_lastname_first: 'Bessette, Benoit',
        personId: 9141,
        text: 'Benoit Bessette',
        type_contenu: 'Person',
        uid: null,
      },
      {
        createdAt: '2023-11-02T18:09:12-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 7521,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Vasseur',
        givenName: 'Monique',
        id: '65441e0895f83',
        lastUpdatedAt: null,
        name_with_lastname_first: 'Vasseur, Monique',
        personId: 9142,
        text: 'Monique Vasseur',
        type_contenu: 'Person',
        uid: null,
      },
    ],
    statutInfrastructure: {
      descriptions: {
        fr: null,
      },
      id: 2,
      nom: 'Actif',
      noms: {
        fr: 'Actif',
      },
      slug: 'actif',
      text: 'En acquisition',
      uid: 'en-acquisition',
    },
    telephone: null,
    text: "Plateforme d'imagerie pour gels et blots",
    totalEquipements: 15,
    typeContenu: 'Infrastructure',
    typeInfrastructure: {
      descriptions: {
        fr: null,
      },
      id: 2,
      nom: 'Plateforme',
      noms: {
        fr: 'Plateforme',
      },
      slug: 'plateforme',
      text: 'Plateforme',
      uid: 'plateforme',
    },
    uid: 'ur14384',
    unite: {
      typeUnite: {
        descriptions: {
          fr: null,
        },
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      acronym: null,
      acronyms: {
        fr: null,
      },
      createdAt: '2023-11-01T11:41:50-0400',
      dateBegin: null,
      dateEnd: null,
      descriptions: {
        fr: null,
      },
      id: '654271beb6617',
      lastUpdatedAt: '2023-11-01T11:41:50-0400',
      names: {
        fr: 'Faculté de médecine - Département de biochimie et médecine moléculaire',
      },
      organizationId: 627,
      parent: {
        acronym: null,
        acronyms: {
          en: null,
          fr: null,
        },
        createdAt: '2023-11-01T11:28:35-0400',
        dateBegin: null,
        dateEnd: null,
        descriptions: {
          en: null,
          fr: null,
        },
        id: 'G000001',
        lastUpdatedAt: '2023-11-01T11:28:35-0400',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        organizationId: 1,
        parent: null,
        pseudonymes: {
          en: null,
          fr: null,
        },
        text: 'Université de Montréal',
        typeContenu: 'Etablissement',
        typeUnite: {
          descriptions: {
            fr: null,
          },
          id: 1,
          nom: 'Académique',
          noms: {
            fr: 'Académique',
          },
          slug: 'academique',
          text: 'Académique',
          uid: 'academique',
        },
        uid: 'G000001',
      },
      pseudonymes: {
        fr: null,
      },
      text: 'Faculté de médecine - Département de biochimie et médecine moléculaire',
      typeContenu: 'Unite',
      uid: 'D501A0',
    },
    url: 'https://biochimie.umontreal.ca/plateformes-scientifiques-bmm/imagerie-de-gels-et-blots/',
    visibilite: {
      descriptions: {
        fr: null,
      },
      id: 1,
      nom: 'Public',
      noms: {
        fr: 'Public',
      },
      slug: 'public',
      text: 'Public',
      uid: 'public',
    },
  },
  {
    batimentLocaux: [
      {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '2',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Roger-Gaudry',
        noms: {
          fr: 'Roger-Gaudry',
        },
        pseudonyme: null,
        slug: null,
        text: 'Roger-Gaudry - Campus principal',
        uid: '511A',
      },
    ],
    campusLocaux: [
      {
        etablissement: {
          id: 'G000001',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          text: 'Université de Montréal',
          uid: 'G000001',
        },
        id: '1',
        nom: 'Campus principal',
        noms: {
          fr: 'Campus principal',
        },
        pseudonyme: null,
        slug: 'campus-principal',
        text: 'Campus principal',
        uid: 'mtl',
      },
    ],
    createdAt: '2023-11-02T18:07:35-0400',
    descriptions: {
      en: null,
      fr: null,
    },
    directeursTechnique: [
      {
        createdAt: '2023-11-01T11:46:54-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 743,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Legault',
        givenName: 'Pascale',
        id: '654272ee9c435',
        lastUpdatedAt: '2023-11-01T11:46:54-0400',
        name_with_lastname_first: 'Legault, Pascale',
        personId: 835,
        text: 'Pascale Legault',
        type_contenu: 'Person',
        uid: 'in14566',
      },
    ],
    emplacement: {
      batiment: {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '2',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Roger-Gaudry',
        noms: {
          fr: 'Roger-Gaudry',
        },
        pseudonyme: null,
        slug: null,
        text: 'Roger-Gaudry - Campus principal',
        uid: '511A',
      },
      estLocal: true,
      id: 2,
      local: null,
      territory: null,
      text: 'Roger-Gaudry - Campus principal',
    },
    id: 'I000386',
    ID: 'infrastructure.I000386',
    infrastructureId: 19,
    infrastructureReception: {
      batiment: {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '2',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Roger-Gaudry',
        noms: {
          fr: 'Roger-Gaudry',
        },
        pseudonyme: null,
        slug: null,
        text: 'Roger-Gaudry - Campus principal',
        uid: '511A',
      },
      estLocal: true,
      id: 2,
      local: null,
      territory: null,
      text: 'Roger-Gaudry - Campus principal',
    },
    juridiction: {
      acronym: null,
      acronyms: {
        en: null,
        fr: null,
      },
      createdAt: '2023-11-01T11:28:35-0400',
      dateBegin: null,
      dateEnd: null,
      descriptions: {
        en: null,
        fr: null,
      },
      id: 'G000001',
      lastUpdatedAt: '2023-11-01T11:28:35-0400',
      names: {
        en: 'University of Montreal',
        fr: 'Université de Montréal',
      },
      organizationId: 1,
      parent: null,
      pseudonymes: {
        en: null,
        fr: null,
      },
      text: 'Université de Montréal',
      typeContenu: 'Etablissement',
      typeEtablissement: {
        descriptions: {
          fr: null,
        },
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      uid: 'G000001',
    },
    laboratoireInnovations: [],
    lastUpdatedAt: '2023-11-02T18:07:35-0400',
    locaux: [],
    mettreEnVedette: false,
    nom: 'Plateforme de centrifugation',
    noms: {
      en: 'centrifugation',
      fr: 'Plateforme de centrifugation',
    },
    projetFinancements: [],
    pseudonyme: 'BMM-Centrifugeuses',
    responsablesSST: [],
    responsablesTechnique: [
      {
        createdAt: '2023-11-02T18:09:12-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 7520,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Bessette',
        givenName: 'Benoit',
        id: '65441e089518c',
        lastUpdatedAt: null,
        name_with_lastname_first: 'Bessette, Benoit',
        personId: 9141,
        text: 'Benoit Bessette',
        type_contenu: 'Person',
        uid: null,
      },
    ],
    statutInfrastructure: {
      descriptions: {
        fr: null,
      },
      id: 2,
      nom: 'Actif',
      noms: {
        fr: 'Actif',
      },
      slug: 'actif',
      text: 'Actif',
      uid: 'actif',
    },
    telephone: null,
    text: 'Plateforme de centrifugation',
    totalEquipements: 58,
    typeContenu: 'Infrastructure',
    typeInfrastructure: {
      descriptions: {
        fr: null,
      },
      id: 2,
      nom: 'Plateforme',
      noms: {
        fr: 'Plateforme',
      },
      slug: 'plateforme',
      text: 'Plateforme',
      uid: 'plateforme',
    },
    uid: 'ur14382',
    unite: {
      typeUnite: {
        descriptions: {
          fr: null,
        },
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      acronym: null,
      acronyms: {
        fr: null,
      },
      createdAt: '2023-11-01T11:41:50-0400',
      dateBegin: null,
      dateEnd: null,
      descriptions: {
        fr: null,
      },
      id: '654271beb6617',
      lastUpdatedAt: '2023-11-01T11:41:50-0400',
      names: {
        fr: 'Faculté de médecine - Département de biochimie et médecine moléculaire',
      },
      organizationId: 627,
      parent: {
        acronym: null,
        acronyms: {
          en: null,
          fr: null,
        },
        createdAt: '2023-11-01T11:28:35-0400',
        dateBegin: null,
        dateEnd: null,
        descriptions: {
          en: null,
          fr: null,
        },
        id: 'G000001',
        lastUpdatedAt: '2023-11-01T11:28:35-0400',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        organizationId: 1,
        parent: null,
        pseudonymes: {
          en: null,
          fr: null,
        },
        text: 'Université de Montréal',
        typeContenu: 'Etablissement',
        typeUnite: {
          descriptions: {
            fr: null,
          },
          id: 1,
          nom: 'Académique',
          noms: {
            fr: 'Académique',
          },
          slug: 'academique',
          text: 'Académique',
          uid: 'academique',
        },
        uid: 'G000001',
      },
      pseudonymes: {
        fr: null,
      },
      text: 'Faculté de médecine - Département de biochimie et médecine moléculaire',
      typeContenu: 'Unite',
      uid: 'D501A0',
    },
    url: 'https://biochimie.umontreal.ca/plateformes-scientifiques-bmm/ultracentrifugation/',
    visibilite: {
      descriptions: {
        fr: null,
      },
      id: 1,
      nom: 'Public',
      noms: {
        fr: 'Public',
      },
      slug: 'public',
      text: 'Public',
      uid: 'public',
    },
  },
  {
    batimentLocaux: [
      {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '2',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Roger-Gaudry',
        noms: {
          fr: 'Roger-Gaudry',
        },
        pseudonyme: null,
        slug: null,
        text: 'Roger-Gaudry - Campus principal',
        uid: '511A',
      },
    ],
    campusLocaux: [
      {
        etablissement: {
          id: 'G000001',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },

          text: 'Université de Montréal',

          uid: 'G000001',
        },
        id: '1',
        nom: 'Campus principal',
        noms: {
          fr: 'Campus principal',
        },
        pseudonyme: null,
        slug: 'campus-principal',
        text: 'Campus principal',
        uid: 'mtl',
      },
    ],
    createdAt: '2023-11-02T18:07:35-0400',
    descriptions: {
      en: null,
      fr: null,
    },
    directeursTechnique: [
      {
        createdAt: '2023-11-01T11:46:26-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 561,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Ferbeyre',
        givenName: 'Gerardo',
        id: '654272d25012a',
        lastUpdatedAt: '2023-11-01T11:46:26-0400',
        name_with_lastname_first: 'Ferbeyre, Gerardo',
        personId: 637,
        text: 'Gerardo Ferbeyre',
        type_contenu: 'Person',
        uid: 'in14346',
      },
    ],
    emplacement: {
      batiment: {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '2',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Roger-Gaudry',
        noms: {
          fr: 'Roger-Gaudry',
        },
        pseudonyme: null,
        slug: null,
        text: 'Roger-Gaudry - Campus principal',
        uid: '511A',
      },
      estLocal: true,
      id: 2,
      local: null,
      territory: null,
      text: 'Roger-Gaudry - Campus principal',
    },
    id: 'I000254',
    ID: 'infrastructure.I000254',
    infrastructureId: 21,
    infrastructureReception: {
      batiment: {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '2',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Roger-Gaudry',
        noms: {
          fr: 'Roger-Gaudry',
        },
        pseudonyme: null,
        slug: null,
        text: 'Roger-Gaudry - Campus principal',
        uid: '511A',
      },
      estLocal: true,
      id: 2,
      local: null,
      territory: null,
      text: 'Roger-Gaudry - Campus principal',
    },
    juridiction: {
      acronym: null,
      acronyms: {
        en: null,
        fr: null,
      },
      createdAt: '2023-11-01T11:28:35-0400',
      dateBegin: null,
      dateEnd: null,
      descriptions: {
        en: null,
        fr: null,
      },
      id: 'G000001',
      lastUpdatedAt: '2023-11-01T11:28:35-0400',
      names: {
        en: 'University of Montreal',
        fr: 'Université de Montréal',
      },
      organizationId: 1,
      parent: null,
      pseudonymes: {
        en: null,
        fr: null,
      },
      text: 'Université de Montréal',
      typeContenu: 'Etablissement',
      typeEtablissement: {
        descriptions: {
          fr: null,
        },
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      uid: 'G000001',
    },
    laboratoireInnovations: [],
    lastUpdatedAt: '2023-11-02T18:07:35-0400',
    locaux: [],
    mettreEnVedette: false,
    nom: 'Plateforme de protéomique et génomique',
    noms: {
      en: 'proteomic and genomic',
      fr: 'Plateforme de protéomique et génomique',
    },
    projetFinancements: [],
    pseudonyme: 'BMM-Protéomique et génomique',
    responsablesSST: [],
    responsablesTechnique: [
      {
        createdAt: '2023-11-02T18:09:12-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 7520,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Bessette',
        givenName: 'Benoit',
        id: '65441e089518c',
        lastUpdatedAt: null,
        name_with_lastname_first: 'Bessette, Benoit',
        personId: 9141,
        text: 'Benoit Bessette',
        type_contenu: 'Person',
        uid: null,
      },
    ],
    statutInfrastructure: {
      descriptions: {
        fr: null,
      },
      id: 2,
      nom: 'Actif',
      noms: {
        fr: 'Actif',
      },
      slug: 'actif',
      text: 'Actif',
      uid: 'actif',
    },
    telephone: null,
    text: 'Plateforme de protéomique et génomique',
    totalEquipements: 8,
    typeContenu: 'Infrastructure',
    typeInfrastructure: {
      descriptions: {
        fr: null,
      },
      id: 2,
      nom: 'Plateforme',
      noms: {
        fr: 'Plateforme',
      },
      slug: 'plateforme',
      text: 'Plateforme',
      uid: 'plateforme',
    },
    uid: 'ur14190',
    unite: {
      typeUnite: {
        descriptions: {
          fr: null,
        },
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      acronym: null,
      acronyms: {
        fr: null,
      },
      createdAt: '2023-11-01T11:41:50-0400',
      dateBegin: null,
      dateEnd: null,
      descriptions: {
        fr: null,
      },
      id: '654271beb6617',
      lastUpdatedAt: '2023-11-01T11:41:50-0400',
      names: {
        fr: 'Faculté de médecine - Département de biochimie et médecine moléculaire',
      },
      organizationId: 627,
      parent: {
        acronym: null,
        acronyms: {
          en: null,
          fr: null,
        },
        createdAt: '2023-11-01T11:28:35-0400',
        dateBegin: null,
        dateEnd: null,
        descriptions: {
          en: null,
          fr: null,
        },
        id: 'G000001',
        lastUpdatedAt: '2023-11-01T11:28:35-0400',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        organizationId: 1,
        parent: null,
        pseudonymes: {
          en: null,
          fr: null,
        },
        text: 'Université de Montréal',
        typeContenu: 'Etablissement',
        typeUnite: {
          descriptions: {
            fr: null,
          },
          id: 1,
          nom: 'Académique',
          noms: {
            fr: 'Académique',
          },
          slug: 'academique',
          text: 'Académique',
          uid: 'academique',
        },
        uid: 'G000001',
      },
      pseudonymes: {
        fr: null,
      },
      text: 'Faculté de médecine - Département de biochimie et médecine moléculaire',
      typeContenu: 'Unite',
      uid: 'D501A0',
    },
    url: 'https://biochimie.umontreal.ca/plateformes-scientifiques-bmm/proteomique-et-genomique/',
    visibilite: {
      descriptions: {
        fr: null,
      },
      id: 1,
      nom: 'Public',
      noms: {
        fr: 'Public',
      },
      slug: 'public',
      text: 'Public',
      uid: 'public',
    },
  },
  {
    batimentLocaux: [
      {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '2',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Roger-Gaudry',
        noms: {
          fr: 'Roger-Gaudry',
        },
        pseudonyme: null,
        slug: null,
        text: 'Roger-Gaudry - Campus principal',
        uid: '511A',
      },
    ],
    campusLocaux: [
      {
        etablissement: {
          id: 'G000001',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },

          text: 'Université de Montréal',

          uid: 'G000001',
        },
        id: '1',
        nom: 'Campus principal',
        noms: {
          fr: 'Campus principal',
        },
        pseudonyme: null,
        slug: 'campus-principal',
        text: 'Campus principal',
        uid: 'mtl',
      },
    ],
    createdAt: '2023-11-02T18:07:35-0400',
    descriptions: {
      en: null,
      fr: null,
    },
    directeursTechnique: [
      {
        createdAt: '2023-11-01T11:46:54-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 743,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Legault',
        givenName: 'Pascale',
        id: '654272ee9c435',
        lastUpdatedAt: '2023-11-01T11:46:54-0400',
        name_with_lastname_first: 'Legault, Pascale',
        personId: 835,
        text: 'Pascale Legault',
        type_contenu: 'Person',
        uid: 'in14566',
      },
    ],
    emplacement: {
      batiment: {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '2',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Roger-Gaudry',
        noms: {
          fr: 'Roger-Gaudry',
        },
        pseudonyme: null,
        slug: null,
        text: 'Roger-Gaudry - Campus principal',
        uid: '511A',
      },
      estLocal: true,
      id: 2,
      local: null,
      territory: null,
      text: 'Roger-Gaudry - Campus principal',
    },
    id: 'I000001',
    ID: 'infrastructure.I000001',
    infrastructureId: 22,
    infrastructureReception: {
      batiment: {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '2',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Roger-Gaudry',
        noms: {
          fr: 'Roger-Gaudry',
        },
        pseudonyme: null,
        slug: null,
        text: 'Roger-Gaudry - Campus principal',
        uid: '511A',
      },
      estLocal: true,
      id: 2,
      local: null,
      territory: null,
      text: 'Roger-Gaudry - Campus principal',
    },
    juridiction: {
      acronym: null,
      acronyms: {
        en: null,
        fr: null,
      },
      createdAt: '2023-11-01T11:28:35-0400',
      dateBegin: null,
      dateEnd: null,
      descriptions: {
        en: null,
        fr: null,
      },
      id: 'G000001',
      lastUpdatedAt: '2023-11-01T11:28:35-0400',
      names: {
        en: 'University of Montreal',
        fr: 'Université de Montréal',
      },
      organizationId: 1,
      parent: null,
      pseudonymes: {
        en: null,
        fr: null,
      },
      text: 'Université de Montréal',
      typeContenu: 'Etablissement',
      typeEtablissement: {
        descriptions: {
          fr: null,
        },
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      uid: 'G000001',
    },
    laboratoireInnovations: [],
    lastUpdatedAt: '2023-11-02T18:07:35-0400',
    locaux: [],
    mettreEnVedette: false,
    nom: 'Plateforme de biologie structurale',
    noms: {
      en: 'structural biology',
      fr: 'Plateforme de biologie structurale',
    },
    projetFinancements: [],
    pseudonyme: 'BMM-Biologie Structurale. BMM-RMN; RMN Biomoléculaire',
    responsablesSST: [],
    responsablesTechnique: [
      {
        createdAt: '2023-11-02T18:09:12-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 7517,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Cyr',
        givenName: 'Normand',
        id: '65441e085b2c2',
        lastUpdatedAt: null,
        name_with_lastname_first: 'Cyr, Normand',
        personId: 9138,
        text: 'Normand Cyr',
        type_contenu: 'Person',
        uid: null,
      },
      {
        createdAt: '2023-11-02T18:09:12-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 7520,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Bessette',
        givenName: 'Benoit',
        id: '65441e089518c',
        lastUpdatedAt: null,
        name_with_lastname_first: 'Bessette, Benoit',
        personId: 9141,
        text: 'Benoit Bessette',
        type_contenu: 'Person',
        uid: null,
      },
      {
        createdAt: '2023-11-02T18:09:13-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 7523,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Richter',
        givenName: 'Ryan',
        id: '65441e096f9e0',
        lastUpdatedAt: null,
        name_with_lastname_first: 'Richter, Ryan',
        personId: 9144,
        text: 'Ryan Richter',
        type_contenu: 'Person',
        uid: null,
      },
    ],
    statutInfrastructure: {
      descriptions: {
        fr: null,
      },
      id: 2,
      nom: 'Actif',
      noms: {
        fr: 'Actif',
      },
      slug: 'actif',
      text: 'Actif',
      uid: 'actif',
    },
    telephone: null,
    text: 'Plateforme de biologie structurale',
    totalEquipements: 8,
    typeContenu: 'Infrastructure',
    typeInfrastructure: {
      descriptions: {
        fr: null,
      },
      id: 2,
      nom: 'Plateforme',
      noms: {
        fr: 'Plateforme',
      },
      slug: 'plateforme',
      text: 'Plateforme',
      uid: 'plateforme',
    },
    uid: 'ur14385',
    unite: {
      typeUnite: {
        descriptions: {
          fr: null,
        },
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      acronym: null,
      acronyms: {
        fr: null,
      },
      createdAt: '2023-11-01T11:41:50-0400',
      dateBegin: null,
      dateEnd: null,
      descriptions: {
        fr: null,
      },
      id: '654271beb6617',
      lastUpdatedAt: '2023-11-01T11:41:50-0400',
      names: {
        fr: 'Faculté de médecine - Département de biochimie et médecine moléculaire',
      },
      organizationId: 627,
      parent: {
        acronym: null,
        acronyms: {
          en: null,
          fr: null,
        },
        createdAt: '2023-11-01T11:28:35-0400',
        dateBegin: null,
        dateEnd: null,
        descriptions: {
          en: null,
          fr: null,
        },
        id: 'G000001',
        lastUpdatedAt: '2023-11-01T11:28:35-0400',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        organizationId: 1,
        parent: null,
        pseudonymes: {
          en: null,
          fr: null,
        },
        text: 'Université de Montréal',
        typeContenu: 'Etablissement',
        typeUnite: {
          descriptions: {
            fr: null,
          },
          id: 1,
          nom: 'Académique',
          noms: {
            fr: 'Académique',
          },
          slug: 'academique',
          text: 'Académique',
          uid: 'academique',
        },
        uid: 'G000001',
      },
      pseudonymes: {
        fr: null,
      },
      text: 'Faculté de médecine - Département de biochimie et médecine moléculaire',
      typeContenu: 'Unite',
      uid: 'D501A0',
    },
    url: 'https://biochimie.umontreal.ca/plateformes-scientifiques-bmm/biologie-structurale/',
    visibilite: {
      descriptions: {
        fr: null,
      },
      id: 1,
      nom: 'Public',
      noms: {
        fr: 'Public',
      },
      slug: 'public',
      text: 'Public',
      uid: 'public',
    },
  },
  {
    batimentLocaux: [
      {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '2',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Roger-Gaudry',
        noms: {
          fr: 'Roger-Gaudry',
        },
        pseudonyme: null,
        slug: null,
        text: 'Roger-Gaudry - Campus principal',
        uid: '511A',
      },
    ],
    campusLocaux: [
      {
        etablissement: {
          id: 'G000001',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },

          text: 'Université de Montréal',

          uid: 'G000001',
        },
        id: '1',
        nom: 'Campus principal',
        noms: {
          fr: 'Campus principal',
        },
        pseudonyme: null,
        slug: 'campus-principal',
        text: 'Campus principal',
        uid: 'mtl',
      },
    ],
    createdAt: '2023-11-02T18:07:35-0400',
    descriptions: {
      en: null,
      fr: null,
    },
    directeursTechnique: [
      {
        createdAt: '2023-11-01T11:46:54-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 743,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Legault',
        givenName: 'Pascale',
        id: '654272ee9c435',
        lastUpdatedAt: '2023-11-01T11:46:54-0400',
        name_with_lastname_first: 'Legault, Pascale',
        personId: 835,
        text: 'Pascale Legault',
        type_contenu: 'Person',
        uid: 'in14566',
      },
    ],
    emplacement: {
      batiment: {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '2',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Roger-Gaudry',
        noms: {
          fr: 'Roger-Gaudry',
        },
        pseudonyme: null,
        slug: null,
        text: 'Roger-Gaudry - Campus principal',
        uid: '511A',
      },
      estLocal: true,
      id: 2,
      local: null,
      territory: null,
      text: 'Roger-Gaudry - Campus principal',
    },
    id: 'I000388',
    ID: 'infrastructure.I000388',
    infrastructureId: 23,
    infrastructureReception: {
      batiment: {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '2',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Roger-Gaudry',
        noms: {
          fr: 'Roger-Gaudry',
        },
        pseudonyme: null,
        slug: null,
        text: 'Roger-Gaudry - Campus principal',
        uid: '511A',
      },
      estLocal: true,
      id: 2,
      local: null,
      territory: null,
      text: 'Roger-Gaudry - Campus principal',
    },
    juridiction: {
      acronym: null,
      acronyms: {
        en: null,
        fr: null,
      },
      createdAt: '2023-11-01T11:28:35-0400',
      dateBegin: null,
      dateEnd: null,
      descriptions: {
        en: null,
        fr: null,
      },
      id: 'G000001',
      lastUpdatedAt: '2023-11-01T11:28:35-0400',
      names: {
        en: 'University of Montreal',
        fr: 'Université de Montréal',
      },
      organizationId: 1,
      parent: null,
      pseudonymes: {
        en: null,
        fr: null,
      },
      text: 'Université de Montréal',
      typeContenu: 'Etablissement',
      typeEtablissement: {
        descriptions: {
          fr: null,
        },
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      uid: 'G000001',
    },
    laboratoireInnovations: [],
    lastUpdatedAt: '2023-11-02T18:07:35-0400',
    locaux: [],
    mettreEnVedette: false,
    nom: 'Plateforme de stérilisation, eau osmosée et compteurs à scintillation',
    noms: {
      en: 'services (sterilization, purified water, scintillation counter)',
      fr: 'Plateforme de stérilisation, eau osmosée et compteurs à scintillation',
    },
    projetFinancements: [],
    pseudonyme: 'BMM-Stérilisation, eau osmosée et compteurs à scintillation',
    responsablesSST: [],
    responsablesTechnique: [
      {
        createdAt: '2023-11-02T18:09:13-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 7524,
            text: '<EMAIL>',
          },
        ],
        familyName: "D'Astous",
        givenName: 'Linda',
        id: '65441e09a27a2',
        lastUpdatedAt: null,
        name_with_lastname_first: "D'Astous, Linda",
        personId: 9145,
        text: "Linda D'Astous",
        type_contenu: 'Person',
        uid: null,
      },
    ],
    statutInfrastructure: {
      descriptions: {
        fr: null,
      },
      id: 2,
      nom: 'Actif',
      noms: {
        fr: 'Actif',
      },
      slug: 'actif',
      text: 'Actif',
      uid: 'actif',
    },
    telephone: null,
    text: 'Plateforme de stérilisation, eau osmosée et compteurs à scintillation',
    totalEquipements: 3,
    typeContenu: 'Infrastructure',
    typeInfrastructure: {
      descriptions: {
        fr: null,
      },
      id: 2,
      nom: 'Plateforme',
      noms: {
        fr: 'Plateforme',
      },
      slug: 'plateforme',
      text: 'Plateforme',
      uid: 'plateforme',
    },
    uid: 'ur14387',
    unite: {
      typeUnite: {
        descriptions: {
          fr: null,
        },
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      acronym: null,
      acronyms: {
        fr: null,
      },
      createdAt: '2023-11-01T11:41:50-0400',
      dateBegin: null,
      dateEnd: null,
      descriptions: {
        fr: null,
      },
      id: '654271beb6617',
      lastUpdatedAt: '2023-11-01T11:41:50-0400',
      names: {
        fr: 'Faculté de médecine - Département de biochimie et médecine moléculaire',
      },
      organizationId: 627,
      parent: {
        acronym: null,
        acronyms: {
          en: null,
          fr: null,
        },
        createdAt: '2023-11-01T11:28:35-0400',
        dateBegin: null,
        dateEnd: null,
        descriptions: {
          en: null,
          fr: null,
        },
        id: 'G000001',
        lastUpdatedAt: '2023-11-01T11:28:35-0400',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        organizationId: 1,
        parent: null,
        pseudonymes: {
          en: null,
          fr: null,
        },
        text: 'Université de Montréal',
        typeContenu: 'Etablissement',
        typeUnite: {
          descriptions: {
            fr: null,
          },
          id: 1,
          nom: 'Académique',
          noms: {
            fr: 'Académique',
          },
          slug: 'academique',
          text: 'Académique',
          uid: 'academique',
        },
        uid: 'G000001',
      },
      pseudonymes: {
        fr: null,
      },
      text: 'Faculté de médecine - Département de biochimie et médecine moléculaire',
      typeContenu: 'Unite',
      uid: 'D501A0',
    },
    url: 'https://biochimie.umontreal.ca/plateformes-scientifiques-bmm/sterilisation-eau-osmosee-et-compteurs-a-scintillation/',
    visibilite: {
      descriptions: {
        fr: null,
      },
      id: 1,
      nom: 'Public',
      noms: {
        fr: 'Public',
      },
      slug: 'public',
      text: 'Public',
      uid: 'public',
    },
  },
  {
    batimentLocaux: [
      {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '32',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Pavillon Marcelle-Coutu',
        noms: {
          fr: 'Pavillon Marcelle-Coutu',
        },
        pseudonyme: null,
        slug: null,
        text: 'Pavillon Marcelle-Coutu - Campus principal',
        uid: '594A',
      },
    ],
    campusLocaux: [
      {
        etablissement: {
          id: 'G000001',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },

          text: 'Université de Montréal',

          uid: 'G000001',
        },
        id: '1',
        nom: 'Campus principal',
        noms: {
          fr: 'Campus principal',
        },
        pseudonyme: null,
        slug: 'campus-principal',
        text: 'Campus principal',
        uid: 'mtl',
      },
    ],
    createdAt: '2023-11-02T18:07:55-0400',
    descriptions: {
      fr: "La plateforme de bio-imagerie de l'IRIC réunite un appareillage de microscopie et offre des services de pointe aux chercheurs de l'IRIC et de l'Université de Montréal dans le cadre de leurs projets de recherche. Elle accueille aussi des scientifiques externes en provenance du milieu universitaire et de l'industrie dans le cadre de collaborations et d'ententes de recherche.\n\nOutre la préparation d'échantillons, d'analyse et de tri, notre personnel offre un service de consultation et peut former les utilisateurs à l'exploitation des instruments.",
    },
    directeursTechnique: [
      {
        createdAt: '2023-11-01T11:46:43-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 677,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Sauvageau',
        givenName: 'Guy',
        id: '654272e3c05f9',
        lastUpdatedAt: '2023-11-01T11:46:43-0400',
        name_with_lastname_first: 'Sauvageau, Guy',
        personId: 765,
        text: 'Guy Sauvageau',
        type_contenu: 'Person',
        uid: 'in14491',
      },
    ],
    emplacement: {
      batiment: {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '32',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Pavillon Marcelle-Coutu',
        noms: {
          fr: 'Pavillon Marcelle-Coutu',
        },
        pseudonyme: null,
        slug: null,
        text: 'Pavillon Marcelle-Coutu - Campus principal',
        uid: '594A',
      },
      estLocal: true,
      id: 32,
      local: null,
      territory: null,
      text: 'Pavillon Marcelle-Coutu - Campus principal',
    },
    id: 'I000019',
    ID: 'infrastructure.I000019',
    infrastructureId: 269,
    infrastructureReception: {
      batiment: {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '32',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Pavillon Marcelle-Coutu',
        noms: {
          fr: 'Pavillon Marcelle-Coutu',
        },
        pseudonyme: null,
        slug: null,
        text: 'Pavillon Marcelle-Coutu - Campus principal',
        uid: '594A',
      },
      estLocal: true,
      id: 32,
      local: null,
      territory: null,
      text: 'Pavillon Marcelle-Coutu - Campus principal',
    },
    juridiction: {
      acronym: 'IRIC',
      acronyms: {
        fr: 'IRIC',
      },
      createdAt: '2023-11-01T11:40:09-0400',
      dateBegin: null,
      dateEnd: null,
      descriptions: {
        fr: null,
      },
      id: 'A000017',
      lastUpdatedAt: '2023-11-01T11:42:39-0400',
      names: {
        fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
      },
      organizationId: 10,
      parent: {
        acronym: null,
        acronyms: {
          en: null,
          fr: null,
        },
        createdAt: '2023-11-01T11:28:35-0400',
        dateBegin: null,
        dateEnd: null,
        descriptions: {
          en: null,
          fr: null,
        },
        id: 'G000001',
        lastUpdatedAt: '2023-11-01T11:28:35-0400',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        organizationId: 1,
        parent: null,
        pseudonymes: {
          en: null,
          fr: null,
        },
        text: 'Université de Montréal',
        typeContenu: 'Etablissement',
        typeEtablissement: {
          descriptions: {
            fr: null,
          },
          id: 1,
          nom: 'Académique',
          noms: {
            fr: 'Académique',
          },
          slug: 'academique',
          text: 'Académique',
          uid: 'academique',
        },
        uid: 'G000001',
      },
      pseudonymes: {
        fr: null,
      },
      text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
      typeContenu: 'Unite',
      uid: 'ur13772',
    },
    laboratoireInnovations: [],
    lastUpdatedAt: '2023-11-02T18:07:55-0400',
    locaux: [],
    mettreEnVedette: false,
    nom: "Plateforme de bio-imagerie de l'IRIC",
    noms: {
      fr: "Plateforme de bio-imagerie de l'IRIC",
    },
    projetFinancements: [],
    pseudonyme:
      'Institut de recherche en immunologie et en cancérologie - bio-imagerie',
    responsablesSST: [],
    responsablesTechnique: [
      {
        createdAt: '2023-11-02T18:10:08-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 7576,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Charbonneau',
        givenName: 'Christian',
        id: '65441e40e08ea',
        lastUpdatedAt: null,
        name_with_lastname_first: 'Charbonneau, Christian',
        personId: 9197,
        text: 'Christian Charbonneau',
        type_contenu: 'Person',
        uid: null,
      },
    ],
    statutInfrastructure: {
      descriptions: {
        fr: null,
      },
      id: 2,
      nom: 'Actif',
      noms: {
        fr: 'Actif',
      },
      slug: 'actif',
      text: 'Actif',
      uid: 'actif',
    },
    telephone: null,
    text: "Plateforme de bio-imagerie de l'IRIC",
    totalEquipements: 24,
    typeContenu: 'Infrastructure',
    typeInfrastructure: {
      descriptions: {
        fr: null,
      },
      id: 2,
      nom: 'Plateforme',
      noms: {
        fr: 'Plateforme',
      },
      slug: 'plateforme',
      text: 'Plateforme',
      uid: 'plateforme',
    },
    uid: 'ur14187',
    unite: {
      acronym: null,
      acronyms: {
        fr: null,
      },
      createdAt: '2023-11-01T11:41:50-0400',
      dateBegin: null,
      dateEnd: null,
      descriptions: {
        fr: null,
      },
      id: '654271bedf035',
      lastUpdatedAt: '2023-11-01T11:41:50-0400',
      names: {
        fr: 'Faculté de médecine - Département de médecine',
      },
      organizationId: 633,
      typeUnite: {
        descriptions: {
          fr: null,
        },
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      parent: {
        acronym: null,
        acronyms: {
          en: null,
          fr: null,
        },
        createdAt: '2023-11-01T11:28:35-0400',
        dateBegin: null,
        dateEnd: null,
        descriptions: {
          en: null,
          fr: null,
        },
        id: 'G000001',
        lastUpdatedAt: '2023-11-01T11:28:35-0400',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        organizationId: 1,
        parent: null,
        pseudonymes: {
          en: null,
          fr: null,
        },
        text: 'Université de Montréal',
        typeContenu: 'Etablissement',
        typeUnite: {
          descriptions: {
            fr: null,
          },
          id: 1,
          nom: 'Académique',
          noms: {
            fr: 'Académique',
          },
          slug: 'academique',
          text: 'Académique',
          uid: 'academique',
        },
        uid: 'G000001',
      },
      pseudonymes: {
        fr: null,
      },
      text: 'Faculté de médecine - Département de médecine',
      typeContenu: 'Unite',
      uid: 'D203A0',
    },
    url: 'https://www.iric.ca/recherche/infrastructures/bio-imagerie/',
    visibilite: {
      descriptions: {
        fr: null,
      },
      id: 1,
      nom: 'Public',
      noms: {
        fr: 'Public',
      },
      slug: 'public',
      text: 'Public',
      uid: 'public',
    },
  },
  {
    batimentLocaux: [
      {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '32',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Pavillon Marcelle-Coutu',
        noms: {
          fr: 'Pavillon Marcelle-Coutu',
        },
        pseudonyme: null,
        slug: null,
        text: 'Pavillon Marcelle-Coutu - Campus principal',
        uid: '594A',
      },
    ],
    campusLocaux: [
      {
        etablissement: {
          id: 'G000001',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          text: 'Université de Montréal',

          uid: 'G000001',
        },
        id: '1',
        nom: 'Campus principal',
        noms: {
          fr: 'Campus principal',
        },
        pseudonyme: null,
        slug: 'campus-principal',
        text: 'Campus principal',
        uid: 'mtl',
      },
    ],
    createdAt: '2023-11-02T18:07:55-0400',
    descriptions: {
      fr: "La plateforme de bioinformatique offre des services de développement algorithmiques et d'analyses personnalisées de données incluant le calcul de haute performance, la gestion des bases de données et le développement de logiciel.\n\nElle représente un atout important pour l'analyse des données générées par les différents groupes de recherche et sert de support technologique pour les autres plateformes de l'Institut.",
    },
    directeursTechnique: [
      {
        createdAt: '2023-11-01T11:46:43-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 677,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Sauvageau',
        givenName: 'Guy',
        id: '654272e3c05f9',
        lastUpdatedAt: '2023-11-01T11:46:43-0400',
        name_with_lastname_first: 'Sauvageau, Guy',
        personId: 765,
        text: 'Guy Sauvageau',
        type_contenu: 'Person',
        uid: 'in14491',
      },
    ],
    emplacement: {
      batiment: {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '32',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Pavillon Marcelle-Coutu',
        noms: {
          fr: 'Pavillon Marcelle-Coutu',
        },
        pseudonyme: null,
        slug: null,
        text: 'Pavillon Marcelle-Coutu - Campus principal',
        uid: '594A',
      },
      estLocal: true,
      id: 32,
      local: null,
      territory: null,
      text: 'Pavillon Marcelle-Coutu - Campus principal',
    },
    id: 'I000020',
    ID: 'infrastructure.I000020',
    infrastructureId: 270,
    infrastructureReception: {
      batiment: {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '32',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Pavillon Marcelle-Coutu',
        noms: {
          fr: 'Pavillon Marcelle-Coutu',
        },
        pseudonyme: null,
        slug: null,
        text: 'Pavillon Marcelle-Coutu - Campus principal',
        uid: '594A',
      },
      estLocal: true,
      id: 32,
      local: null,
      territory: null,
      text: 'Pavillon Marcelle-Coutu - Campus principal',
    },
    juridiction: {
      acronym: 'IRIC',
      acronyms: {
        fr: 'IRIC',
      },
      createdAt: '2023-11-01T11:40:09-0400',
      dateBegin: null,
      dateEnd: null,
      descriptions: {
        fr: null,
      },
      id: 'A000017',
      lastUpdatedAt: '2023-11-01T11:42:39-0400',
      names: {
        fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
      },
      organizationId: 10,
      parent: {
        acronym: null,
        acronyms: {
          en: null,
          fr: null,
        },
        createdAt: '2023-11-01T11:28:35-0400',
        dateBegin: null,
        dateEnd: null,
        descriptions: {
          en: null,
          fr: null,
        },
        id: 'G000001',
        lastUpdatedAt: '2023-11-01T11:28:35-0400',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        organizationId: 1,
        parent: null,
        pseudonymes: {
          en: null,
          fr: null,
        },
        text: 'Université de Montréal',
        typeContenu: 'Etablissement',
        typeEtablissement: {
          descriptions: {
            fr: null,
          },
          id: 1,
          nom: 'Académique',
          noms: {
            fr: 'Académique',
          },
          slug: 'academique',
          text: 'Académique',
          uid: 'academique',
        },
        uid: 'G000001',
      },
      pseudonymes: {
        fr: null,
      },
      text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
      typeContenu: 'Unite',
      uid: 'ur13772',
    },
    laboratoireInnovations: [],
    lastUpdatedAt: '2023-11-02T18:07:55-0400',
    locaux: [],
    mettreEnVedette: false,
    nom: "Plateforme de bio-informatique de l'IRIC",
    noms: {
      fr: "Plateforme de bio-informatique de l'IRIC",
    },
    projetFinancements: [],
    pseudonyme:
      'Institut de recherche en immunologie et en cancérologie - bio-informatique',
    responsablesSST: [],
    responsablesTechnique: [
      {
        createdAt: '2023-11-02T18:10:09-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 7577,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Gendron',
        givenName: 'Patrick',
        id: '65441e4126207',
        lastUpdatedAt: null,
        name_with_lastname_first: 'Gendron, Patrick',
        personId: 9198,
        text: 'Patrick Gendron',
        type_contenu: 'Person',
        uid: null,
      },
    ],
    statutInfrastructure: {
      descriptions: {
        fr: null,
      },
      id: 2,
      nom: 'Actif',
      noms: {
        fr: 'Actif',
      },
      slug: 'actif',
      text: 'Actif',
      uid: 'actif',
    },
    telephone: null,
    text: "Plateforme de bio-informatique de l'IRIC",
    totalEquipements: 2,
    typeContenu: 'Infrastructure',
    typeInfrastructure: {
      descriptions: {
        fr: null,
      },
      id: 2,
      nom: 'Plateforme',
      noms: {
        fr: 'Plateforme',
      },
      slug: 'plateforme',
      text: 'Plateforme',
      uid: 'plateforme',
    },
    uid: 'ur14188',
    unite: {
      typeUnite: {
        descriptions: {
          fr: null,
        },
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      acronym: null,
      acronyms: {
        fr: null,
      },
      createdAt: '2023-11-01T11:41:50-0400',
      dateBegin: null,
      dateEnd: null,
      descriptions: {
        fr: null,
      },
      id: '654271bedf035',
      lastUpdatedAt: '2023-11-01T11:41:50-0400',
      names: {
        fr: 'Faculté de médecine - Département de médecine',
      },
      organizationId: 633,
      parent: {
        acronym: null,
        acronyms: {
          en: null,
          fr: null,
        },
        createdAt: '2023-11-01T11:28:35-0400',
        dateBegin: null,
        dateEnd: null,
        descriptions: {
          en: null,
          fr: null,
        },
        id: 'G000001',
        lastUpdatedAt: '2023-11-01T11:28:35-0400',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        organizationId: 1,
        parent: null,
        pseudonymes: {
          en: null,
          fr: null,
        },
        text: 'Université de Montréal',
        typeContenu: 'Etablissement',
        typeUnite: {
          descriptions: {
            fr: null,
          },
          id: 1,
          nom: 'Académique',
          noms: {
            fr: 'Académique',
          },
          slug: 'academique',
          text: 'Académique',
          uid: 'academique',
        },
        uid: 'G000001',
      },
      pseudonymes: {
        fr: null,
      },
      text: 'Faculté de médecine - Département de médecine',
      typeContenu: 'Unite',
      uid: 'D203A0',
    },
    url: 'https://www.iric.ca/recherche/infrastructures/bio-informatique/',
    visibilite: {
      descriptions: {
        fr: null,
      },
      id: 1,
      nom: 'Public',
      noms: {
        fr: 'Public',
      },
      slug: 'public',
      text: 'Public',
      uid: 'public',
    },
  },
  {
    batimentLocaux: [
      {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '32',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Pavillon Marcelle-Coutu',
        noms: {
          fr: 'Pavillon Marcelle-Coutu',
        },
        pseudonyme: null,
        slug: null,
        text: 'Pavillon Marcelle-Coutu - Campus principal',
        uid: '594A',
      },
    ],
    campusLocaux: [
      {
        etablissement: {
          id: 'G000001',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          text: 'Université de Montréal',
          uid: 'G000001',
        },
        id: '1',
        nom: 'Campus principal',
        noms: {
          fr: 'Campus principal',
        },
        pseudonyme: null,
        slug: 'campus-principal',
        text: 'Campus principal',
        uid: 'mtl',
      },
    ],
    createdAt: '2023-11-02T18:07:55-0400',
    descriptions: {
      fr: 'L’institut héberge une des plus grandes animaleries au Canada offrant un secteur exempt de pathogènes, un secteur conventionnel et une quarantaine autonome. En plus de l’hébergement et des supports technique et vétérinaire pour la recherche et la santé animale, l’animalerie de l’institut collabore avec la plateforme de biopharmacie de la Faculté de pharmacie de l’Université de Montréal pour offrir des services de pharmacocinétique, bioanalyse et toxicologie (www.pfbio.ca) .',
    },
    directeursTechnique: [
      {
        createdAt: '2023-11-01T11:46:43-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 677,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Sauvageau',
        givenName: 'Guy',
        id: '654272e3c05f9',
        lastUpdatedAt: '2023-11-01T11:46:43-0400',
        name_with_lastname_first: 'Sauvageau, Guy',
        personId: 765,
        text: 'Guy Sauvageau',
        type_contenu: 'Person',
        uid: 'in14491',
      },
    ],
    emplacement: {
      batiment: {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '32',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Pavillon Marcelle-Coutu',
        noms: {
          fr: 'Pavillon Marcelle-Coutu',
        },
        pseudonyme: null,
        slug: null,
        text: 'Pavillon Marcelle-Coutu - Campus principal',
        uid: '594A',
      },
      estLocal: true,
      id: 32,
      local: null,
      territory: null,
      text: 'Pavillon Marcelle-Coutu - Campus principal',
    },
    id: 'I000018',
    ID: 'infrastructure.I000018',
    infrastructureId: 271,
    infrastructureReception: {
      batiment: {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '32',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Pavillon Marcelle-Coutu',
        noms: {
          fr: 'Pavillon Marcelle-Coutu',
        },
        pseudonyme: null,
        slug: null,
        text: 'Pavillon Marcelle-Coutu - Campus principal',
        uid: '594A',
      },
      estLocal: true,
      id: 32,
      local: null,
      territory: null,
      text: 'Pavillon Marcelle-Coutu - Campus principal',
    },
    juridiction: {
      acronym: 'IRIC',
      acronyms: {
        fr: 'IRIC',
      },
      createdAt: '2023-11-01T11:40:09-0400',
      dateBegin: null,
      dateEnd: null,
      descriptions: {
        fr: null,
      },
      id: 'A000017',
      lastUpdatedAt: '2023-11-01T11:42:39-0400',
      names: {
        fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
      },
      organizationId: 10,
      parent: {
        acronym: null,
        acronyms: {
          en: null,
          fr: null,
        },
        createdAt: '2023-11-01T11:28:35-0400',
        dateBegin: null,
        dateEnd: null,
        descriptions: {
          en: null,
          fr: null,
        },
        id: 'G000001',
        lastUpdatedAt: '2023-11-01T11:28:35-0400',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        organizationId: 1,
        parent: null,
        pseudonymes: {
          en: null,
          fr: null,
        },
        text: 'Université de Montréal',
        typeContenu: 'Etablissement',
        typeEtablissement: {
          descriptions: {
            fr: null,
          },
          id: 1,
          nom: 'Académique',
          noms: {
            fr: 'Académique',
          },
          slug: 'academique',
          text: 'Académique',
          uid: 'academique',
        },
        uid: 'G000001',
      },
      pseudonymes: {
        fr: null,
      },
      text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
      typeContenu: 'Unite',
      uid: 'ur13772',
    },
    laboratoireInnovations: [],
    lastUpdatedAt: '2023-11-02T18:07:55-0400',
    locaux: [],
    mettreEnVedette: false,
    nom: "Plateforme de biologie in vivo de l'IRIC",
    noms: {
      fr: "Plateforme de biologie in vivo de l'IRIC",
    },
    projetFinancements: [],
    pseudonyme:
      'Institut de recherche en immunologie et en cancérologie - animalerie. IRIC animalerie',
    responsablesSST: [],
    responsablesTechnique: [
      {
        createdAt: '2023-11-02T18:10:09-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 7578,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Julie',
        givenName: 'Gervais',
        id: '65441e41623ba',
        lastUpdatedAt: null,
        name_with_lastname_first: 'Julie, Gervais',
        personId: 9199,
        text: 'Gervais Julie',
        type_contenu: 'Person',
        uid: null,
      },
    ],
    statutInfrastructure: {
      descriptions: {
        fr: null,
      },
      id: 2,
      nom: 'Actif',
      noms: {
        fr: 'Actif',
      },
      slug: 'actif',
      text: 'Actif',
      uid: 'actif',
    },
    telephone: null,
    text: "Plateforme de biologie in vivo de l'IRIC",
    totalEquipements: 54,
    typeContenu: 'Infrastructure',
    typeInfrastructure: {
      descriptions: {
        fr: null,
      },
      id: 2,
      nom: 'Plateforme',
      noms: {
        fr: 'Plateforme',
      },
      slug: 'plateforme',
      text: 'Plateforme',
      uid: 'plateforme',
    },
    uid: 'ur14388',
    unite: {
      typeUnite: {
        descriptions: {
          fr: null,
        },
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      acronym: null,
      acronyms: {
        fr: null,
      },
      createdAt: '2023-11-01T11:41:50-0400',
      dateBegin: null,
      dateEnd: null,
      descriptions: {
        fr: null,
      },
      id: '654271bedf035',
      lastUpdatedAt: '2023-11-01T11:41:50-0400',
      names: {
        fr: 'Faculté de médecine - Département de médecine',
      },
      organizationId: 633,
      parent: {
        acronym: null,
        acronyms: {
          en: null,
          fr: null,
        },
        createdAt: '2023-11-01T11:28:35-0400',
        dateBegin: null,
        dateEnd: null,
        descriptions: {
          en: null,
          fr: null,
        },
        id: 'G000001',
        lastUpdatedAt: '2023-11-01T11:28:35-0400',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        organizationId: 1,
        parent: null,
        pseudonymes: {
          en: null,
          fr: null,
        },
        text: 'Université de Montréal',
        typeContenu: 'Etablissement',
        typeUnite: {
          descriptions: {
            fr: null,
          },
          id: 1,
          nom: 'Académique',
          noms: {
            fr: 'Académique',
          },
          slug: 'academique',
          text: 'Académique',
          uid: 'academique',
        },
        uid: 'G000001',
      },
      pseudonymes: {
        fr: null,
      },
      text: 'Faculté de médecine - Département de médecine',
      typeContenu: 'Unite',
      uid: 'D203A0',
    },
    url: null,
    visibilite: {
      descriptions: {
        fr: null,
      },
      id: 1,
      nom: 'Public',
      noms: {
        fr: 'Public',
      },
      slug: 'public',
      text: 'Public',
      uid: 'public',
    },
  },
  {
    batimentLocaux: [
      {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '32',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Pavillon Marcelle-Coutu',
        noms: {
          fr: 'Pavillon Marcelle-Coutu',
        },
        pseudonyme: null,
        slug: null,
        text: 'Pavillon Marcelle-Coutu - Campus principal',
        uid: '594A',
      },
    ],
    campusLocaux: [
      {
        etablissement: {
          id: 'G000001',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          text: 'Université de Montréal',
          uid: 'G000001',
        },
        id: '1',
        nom: 'Campus principal',
        noms: {
          fr: 'Campus principal',
        },
        pseudonyme: null,
        slug: 'campus-principal',
        text: 'Campus principal',
        uid: 'mtl',
      },
    ],
    createdAt: '2023-11-02T18:07:56-0400',
    descriptions: {
      fr: "La plateforme de biophysique de l'IRIC fournit une solution d'avant-garde en spectroscopie par RMN avec un accent particulier sur les expériences portant sur l'étude de la structure des protéines, des interactions protéine-ligand et des analyses de routine de petites molécules. La plateforme possède deux spectromètres de RMN de Varian: un 600 MHz INOVA équipé d'une sonde froide HCN principalement dédiée aux grosses molécules (protéines / acides nucléiques) et un 400 MHz MR avec un système automatisé de présentation d'échantillons couramment utilisé pour l'analyse de petites molécules.",
    },
    directeursTechnique: [
      {
        createdAt: '2023-11-01T11:46:43-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 677,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Sauvageau',
        givenName: 'Guy',
        id: '654272e3c05f9',
        lastUpdatedAt: '2023-11-01T11:46:43-0400',
        name_with_lastname_first: 'Sauvageau, Guy',
        personId: 765,
        text: 'Guy Sauvageau',
        type_contenu: 'Person',
        uid: 'in14491',
      },
    ],
    emplacement: {
      batiment: {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '32',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Pavillon Marcelle-Coutu',
        noms: {
          fr: 'Pavillon Marcelle-Coutu',
        },
        pseudonyme: null,
        slug: null,
        text: 'Pavillon Marcelle-Coutu - Campus principal',
        uid: '594A',
      },
      estLocal: true,
      id: 32,
      local: null,
      territory: null,
      text: 'Pavillon Marcelle-Coutu - Campus principal',
    },
    id: 'I000021',
    ID: 'infrastructure.I000021',
    infrastructureId: 272,
    infrastructureReception: {
      batiment: {
        campus: {
          etablissement: {
            id: 'G000001',

            names: {
              en: 'University of Montreal',
              fr: 'Université de Montréal',
            },
            text: 'Université de Montréal',
            uid: 'G000001',
          },
          id: '1',
          nom: 'Campus principal',
          noms: {
            fr: 'Campus principal',
          },
          pseudonyme: null,
          slug: 'campus-principal',
          text: 'Campus principal',
          uid: 'mtl',
        },
        id: '32',
        juridiction: {
          acronym: null,
          acronyms: {
            en: null,
            fr: null,
          },
          createdAt: '2023-11-01T11:28:35-0400',
          dateBegin: null,
          dateEnd: null,
          descriptions: {
            en: null,
            fr: null,
          },
          id: 'G000001',
          lastUpdatedAt: '2023-11-01T11:28:35-0400',
          names: {
            en: 'University of Montreal',
            fr: 'Université de Montréal',
          },
          organizationId: 1,
          parent: null,
          pseudonymes: {
            en: null,
            fr: null,
          },
          text: 'Université de Montréal',
          typeContenu: 'Etablissement',
          typeEtablissement: {
            descriptions: {
              fr: null,
            },
            id: 1,
            nom: 'Académique',
            noms: {
              fr: 'Académique',
            },
            slug: 'academique',
            text: 'Académique',
            uid: 'academique',
          },
          uid: 'G000001',
        },
        nom: 'Pavillon Marcelle-Coutu',
        noms: {
          fr: 'Pavillon Marcelle-Coutu',
        },
        pseudonyme: null,
        slug: null,
        text: 'Pavillon Marcelle-Coutu - Campus principal',
        uid: '594A',
      },
      estLocal: true,
      id: 32,
      local: null,
      territory: null,
      text: 'Pavillon Marcelle-Coutu - Campus principal',
    },
    juridiction: {
      acronym: 'IRIC',
      acronyms: {
        fr: 'IRIC',
      },
      createdAt: '2023-11-01T11:40:09-0400',
      dateBegin: null,
      dateEnd: null,
      descriptions: {
        fr: null,
      },
      id: 'A000017',
      lastUpdatedAt: '2023-11-01T11:42:39-0400',
      names: {
        fr: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
      },
      organizationId: 10,
      parent: {
        acronym: null,
        acronyms: {
          en: null,
          fr: null,
        },
        createdAt: '2023-11-01T11:28:35-0400',
        dateBegin: null,
        dateEnd: null,
        descriptions: {
          en: null,
          fr: null,
        },
        id: 'G000001',
        lastUpdatedAt: '2023-11-01T11:28:35-0400',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        organizationId: 1,
        parent: null,
        pseudonymes: {
          en: null,
          fr: null,
        },
        text: 'Université de Montréal',
        typeContenu: 'Etablissement',
        typeEtablissement: {
          descriptions: {
            fr: null,
          },
          id: 1,
          nom: 'Académique',
          noms: {
            fr: 'Académique',
          },
          slug: 'academique',
          text: 'Académique',
          uid: 'academique',
        },
        uid: 'G000001',
      },
      pseudonymes: {
        fr: null,
      },
      text: 'Institut de recherche en immunologie et en cancérologie de l’Université de Montréal',
      typeContenu: 'Unite',
      uid: 'ur13772',
    },
    laboratoireInnovations: [],
    lastUpdatedAt: '2023-11-02T18:07:56-0400',
    locaux: [],
    mettreEnVedette: false,
    nom: "Plateforme de biophysique de l'IRIC",
    noms: {
      fr: "Plateforme de biophysique de l'IRIC",
    },
    projetFinancements: [],
    pseudonyme: null,
    responsablesSST: [],
    responsablesTechnique: [
      {
        createdAt: '2023-11-02T18:10:09-0400',
        emails: [
          {
            address: '<EMAIL>',
            id: 7579,
            text: '<EMAIL>',
          },
        ],
        familyName: 'Osborne',
        givenName: 'Michael',
        id: '65441e419cb4f',
        lastUpdatedAt: null,
        name_with_lastname_first: 'Osborne, Michael',
        personId: 9200,
        text: 'Michael Osborne',
        type_contenu: 'Person',
        uid: null,
      },
    ],
    statutInfrastructure: {
      descriptions: {
        fr: null,
      },
      id: 2,
      nom: 'Actif',
      noms: {
        fr: 'Actif',
      },
      slug: 'actif',
      text: 'Actif',
      uid: 'actif',
    },
    telephone: null,
    text: "Plateforme de biophysique de l'IRIC",
    totalEquipements: 8,
    typeContenu: 'Infrastructure',
    typeInfrastructure: {
      descriptions: {
        fr: null,
      },
      id: 2,
      nom: 'Plateforme',
      noms: {
        fr: 'Plateforme',
      },
      slug: 'plateforme',
      text: 'Plateforme',
      uid: 'plateforme',
    },
    uid: 'ur14389',
    unite: {
      typeUnite: {
        descriptions: {
          fr: null,
        },
        id: 1,
        nom: 'Académique',
        noms: {
          fr: 'Académique',
        },
        slug: 'academique',
        text: 'Académique',
        uid: 'academique',
      },
      acronym: null,
      acronyms: {
        fr: null,
      },
      createdAt: '2023-11-01T11:41:50-0400',
      dateBegin: null,
      dateEnd: null,
      descriptions: {
        fr: null,
      },
      id: '654271bedf035',
      lastUpdatedAt: '2023-11-01T11:41:50-0400',
      names: {
        fr: 'Faculté de médecine - Département de médecine',
      },
      organizationId: 633,
      parent: {
        acronym: null,
        acronyms: {
          en: null,
          fr: null,
        },
        createdAt: '2023-11-01T11:28:35-0400',
        dateBegin: null,
        dateEnd: null,
        descriptions: {
          en: null,
          fr: null,
        },
        id: 'G000001',
        lastUpdatedAt: '2023-11-01T11:28:35-0400',
        names: {
          en: 'University of Montreal',
          fr: 'Université de Montréal',
        },
        organizationId: 1,
        parent: null,
        pseudonymes: {
          en: null,
          fr: null,
        },
        text: 'Université de Montréal',
        typeContenu: 'Etablissement',
        typeUnite: {
          descriptions: {
            fr: null,
          },
          id: 1,
          nom: 'Académique',
          noms: {
            fr: 'Académique',
          },
          slug: 'academique',
          text: 'Académique',
          uid: 'academique',
        },
        uid: 'G000001',
      },
      pseudonymes: {
        fr: null,
      },
      text: 'Faculté de médecine - Département de médecine',
      typeContenu: 'Unite',
      uid: 'D203A0',
    },
    url: 'https://www.iric.ca/recherche/infrastructures/biophysique/',
    visibilite: {
      descriptions: {
        fr: null,
      },
      id: 1,
      nom: 'Public',
      noms: {
        fr: 'Public',
      },
      slug: 'public',
      text: 'Public',
      uid: 'public',
    },
  },
];
